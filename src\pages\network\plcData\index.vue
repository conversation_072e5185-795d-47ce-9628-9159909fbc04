<script lang="ts" setup>
import { useTheme } from 'vuetify'
import { DATE_FILTER_OPTIONS } from '@/utils/constants'

import { getColumnChartConfig, getDonutChartConfig } from '@core/libs/apex-chart/apexCharConfig'

const vuetifyTheme = useTheme()

const userDataSeries = [
  {
    name: 'Apple',
    data: [90, 120, 55, 100, 80, 125, 175, 70, 88],
  },
]

const userChartConfig = computed(() => {
  return {
    ...getColumnChartConfig(vuetifyTheme.current.value),
    colors: ['#6AA1FF'],
    chart: {
      offsetX: 10,
      stacked: true,
      parentHeightOffset: 0,
      toolbar: { show: false },
    },
    tooltip: {
      custom({ series, seriesIndex, dataPointIndex, w }: any) {
        return `
            <div class="chart-tooltip">
              <p class="chart-tooltip-title mb-2">在线用户</p>
              <p class="chart-tooltip-label">时间</p>
              <p class="chart-tooltip-value mb-2">${w.globals.labels[seriesIndex]}</p>
              <p class="chart-tooltip-label">在线用户数量</p>
              <p class="chart-tooltip-value text-primary">${series[seriesIndex][dataPointIndex]}</p>
            </div>
          `
      },
    },
    legend: {},
    plotOptions: {
      bar: {
        columnWidth: '15%',
        borderRadius: 10,
        borderRadiusApplication: 'end',
        borderRadiusWhenStacked: 'all',
      },
    },
    xaxis: {
      type: 'category',
      categories: [
        '2023-10-01T01:00:00.000Z',
        '2023-10-02T02:00:00.000Z',
        '2023-10-03T03:00:00.000Z',
        '2023-10-04T04:00:00.000Z',
        '2023-10-05T05:00:00.000Z',
        '2023-10-06T06:00:00.000Z',
        '2023-10-07T07:00:00.000Z',
        '2023-10-08T08:00:00.000Z',
        '2023-10-09T09:00:00.000Z',
      ],
      labels: {
        formatter(value: any) {
          const date = new Date(value)

          return `${date.getHours()}:${date.getMinutes()}`
        },

      },
    },
  }
})

const pieConfig = computed(() => {
  return {
    ...getDonutChartConfig(vuetifyTheme.current.value),
    labels: ['苹果', '华为', '三星', 'OPPO', 'VIVO', '联想', '一加', '小米', '魅族', '中兴', '其他'],
    tooltip: {
      custom({ series, seriesIndex, dataPointIndex, w }: any) {
        return `
            <div class="chart-tooltip">
              <p class="chart-tooltip-title mb-2">${w.globals.labels[seriesIndex]}</p>
              <p class="chart-tooltip-label">占比</p>
              <p class="chart-tooltip-value mb-2">${w.globals.seriesPercent[seriesIndex][0]}</p>
              <p class="chart-tooltip-label">终端数</p>
              <p class="chart-tooltip-value text-primary">${series[seriesIndex]}</p>
            </div>
          `
      },
    },
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
          label: {
            show: false,
          },
        },
      },
    },
  }
})

const seriesForRation = [85, 16, 50, 50]

const polarChartConfig = computed(() => {
  return {
    chart: {
      type: 'polarArea',
      height: 400,
      toolbar: {
        show: false,
      },
    },
    dataLabels: {
      enabled: true,
      distributed: true,
      style: {
        fontSize: '14px',
        fontWeight: '500',
      },

    },
    series: [19, 17.5, 15],
    labels: ['良好(>=-60dBm)', '普通(>=-75dBm<=-60dBm)', '较差<=-75dBm)'],
    colors: ['#4080FF', '#28C76F', '#FFB269'],
    stroke: {
      width: 1,
      colors: [vuetifyTheme.current.value.colors.surface],
    },
    fill: {
      opacity: 0.8,
    },
    responsive: [{
      breakpoint: 480,
      options: {
        chart: {
          width: 300,
        },
        legend: {
          position: 'bottom',
        },
      },
    }],
    plotOptions: {
      polarArea: {
        rings: {
          strokeWidth: 0,
        },
        spokes: {
          strokeWidth: 0,
        },
      },
    },
    legend: {
      position: 'bottom',
      horizontalAlign: 'center',
    },
    tooltip: {
      custom({ series, seriesIndex, dataPointIndex, w }: any) {
        console.log('polar area ', series, seriesIndex, dataPointIndex, w)

        return `
          <div class="chart-tooltip">
            <p class="chart-tooltip-title mb-2">${w.globals.labels[seriesIndex]}</p>
            <p class="chart-tooltip-label">占比</p>
            <p class="chart-tooltip-value mb-2">${w.globals.seriesPercent[seriesIndex][0]}%</p>
            <p class="chart-tooltip-label">终端数</p>
            <p class="chart-tooltip-value text-success">${series[seriesIndex]}</p>
          </div>
        `
      },
    },
    yaxis: {
      show: false,
    },
  }
})

const productsData = ref({
  total: 3,
  products: [
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: true,
    },
  ],
})

const headers = [
  { title: '终端数据', key: 'name' },
  { title: 'MAC地址', key: 'sn' },
  { title: '上行流量', key: 'no' },
  { title: '下行流量', key: 'ip' },
  { title: '总计流量', key: 'ip' },
  { title: '关联设备', key: 'ip' },
]

const totalProduct = computed(() => productsData.value.products.length + 100)
const page = ref(1)

const date = ref(1)
</script>

<template>
  <div>
    <!-- 👉 products -->
    <VCard class="mb-6">
      <template #title>
        <div class="d-flex justify-space-between">
          <div>在线用户分布</div>
          <div>
            <DateSelector
              v-model:current-value="date"
              :items="DATE_FILTER_OPTIONS"
              item-title="title"
              item-value="value"
            />
          </div>
        </div>
      </template>
      <VueApexCharts
        type="bar"
        height="400"
        :options="userChartConfig"
        :series="userDataSeries"
      />
    </VCard>
    <VRow>
      <VCol cols="4">
        <VCard class="mb-6 ">
          <template #title>
            <div class="d-flex justify-space-between">
              <div>无线终端SSID分布比率</div>
              <div>
                <DateSelector
                  v-model:current-value="date"
                  :items="DATE_FILTER_OPTIONS"
                  item-title="title"
                  item-value="value"
                />
              </div>
            </div>
          </template>
          <VCardText>
            <VueApexCharts
              type="donut"
              height="400"
              :options="pieConfig"
              :series="seriesForRation"
            />
          </VCardText>
        </VCard>
      </VCol>
      <VCol cols="4">
        <VCard class="mb-6 ">
          <template #title>
            <div class="d-flex justify-space-between">
              <div>终端厂商分布比率</div>
              <div>
                <DateSelector
                  v-model:current-value="date"
                  :items="DATE_FILTER_OPTIONS"
                  item-title="title"
                  item-value="value"
                />
              </div>
            </div>
          </template>
          <VCardText>
            <VueApexCharts
              type="donut"
              height="400"
              :options="pieConfig"
              :series="seriesForRation"
            />
          </VCardText>
        </VCard>
      </VCol>
      <VCol cols="4">
        <VCard class="mb-6 ">
          <template #title>
            <div class="d-flex justify-space-between">
              <div>终端信号强度比率 </div>
              <div>
                <DateSelector
                  v-model:current-value="date"
                  :items="DATE_FILTER_OPTIONS"
                  item-title="title"
                  item-value="value"
                />
              </div>
            </div>
          </template>
          <VCardText>
            <!-- <PolarAreaChart :height="400" :chart-data="data" :chart-options="polarAreaConfig" /> -->
            <VueApexCharts
              type="polarArea"
              height="400"
              :options="polarChartConfig"
              :series="polarChartConfig.series"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <VCard class="mb-6 ">
      <template #title>
        <div class="d-flex justify-space-between">
          <div>终端流量排行</div>
          <div>
            <DateSelector
              v-model:current-value="date"
              :items="DATE_FILTER_OPTIONS"
              item-title="title"
              item-value="value"
            />
          </div>
        </div>
      </template>
      <VDivider />
      <VDataTableServer
        :items="productsData.products"
        :headers="headers"
        :items-length="totalProduct"
        :no-data-text="t('NoData')"
      >
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="10"
            :total-items="totalProduct"
          />
        </template>
      </VDataTableServer>
    </VCard>

    <VCard class="">
      <template #title>
        <div class="d-flex justify-space-between">
          <div>终端连接时长排行</div>
          <div>
            <DateSelector
              v-model:current-value="date"
              :items="DATE_FILTER_OPTIONS"
              item-title="title"
              item-value="value"
            />
          </div>
        </div>
      </template>
      <VDivider />
      <VDataTableServer
        :items="productsData.products"
        :headers="headers"
        :items-length="totalProduct"
        :no-data-text="t('NoData')"
      >
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="10"
            :total-items="totalProduct"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </div>
</template>
