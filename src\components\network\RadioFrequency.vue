<script lang="ts" setup>
import { DATE_FILTER_OPTIONS } from '@/utils/constants'

const userDataSeries = ref([{
  name: 'Net Profit',
  data: [44, 55, 57, 56, 61, 58, 63, 60, 66],
}, {
  name: 'Revenue',
  data: [76, 85, 101, 98, 87, 105, 91, 114, 94],
}, {
  name: 'Free Cash Flow',
  data: [35, 41, 36, 26, 45, 48, 52, 53, 41],
}])

const userChartConfig = ref({
  chart: {
    type: 'bar',
    height: 350,
    toolbar: {
      show: false,
    },
  },
  plotOptions: {
    bar: {
      horizontal: false,
      columnWidth: '50%',
    },
  },
  dataLabels: {
    enabled: false,
  },
  stroke: {
    show: true,
    width: 2,
    colors: ['transparent'],
  },
  xaxis: {
    categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct'],
  },
  fill: {
    opacity: 1,
  },
  legend: {
    show: false,
  },
  tooltip: {
    shared: true,
    intersect: false,
    custom({ series, seriesIndex, dataPointIndex, w }: any) {
      return `
            <div class="chart-tooltip">
              <p class="chart-tooltip-title mb-2">${w.globals.labels[dataPointIndex]}</p>
              <p class="chart-tooltip-label">正常</p>
              <p class="chart-tooltip-value text-primary mb-2">${series[0][dataPointIndex]}</p>
              <p class="chart-tooltip-label">空闲</p>
              <p class="chart-tooltip-value text-success mb-2">${series[1][dataPointIndex]}</p>
              <p class="chart-tooltip-label">忙碌</p>
              <p class="chart-tooltip-value text-warning">${series[2][dataPointIndex]}</p>
            </div>
          `
    },
  },
})

const productsData = ref({
  total: 3,
  products: [
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: true,
    },
  ],
})

const headers = [
  { title: '名称', key: 'name' },
  { title: '上传使用率', key: 'sn' },
  { title: '下载使用率', key: 'no' },
  { title: '总使用率', key: 'ip' },
  { title: '峰值时间', key: 'mac' },
]

const totalProduct = computed(() => productsData.value.products.length + 100)
const page = ref(1)

const table2Headers = [
  { title: '名称', key: 'name' },
  { title: '重传率', key: 'sn' },
  { title: '峰值时间', key: 'no' },
]

const table3Headers = [
  { title: '名称', key: 'name' },
  { title: '干扰强度', key: 'sn' },
  { title: '峰值时间', key: 'no' },
]

const table4Headers = [
  { title: '名称', key: 'name' },
  { title: '弱信号占比', key: 'sn' },
  { title: '弱信号出现次数', key: 'no' },
]

const date = ref(0)
const networkType = ref(0)
</script>

<template>
  <div class="radio-frequency">
    <VCard class="mb-6">
      <template #title>
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
        >
          <div class="mr-2 text-h5">
            各信道不通利用率的AP个数统计
          </div>
          <div>
            <BtnGroupSelector
              v-model:value="networkType"
              :options="WIFI_NETWORK_TYPE"
            />
          </div>
        </div>
      </template>
      <VCardText>
        <VueApexCharts
          type="bar"
          height="400"
          :options="userChartConfig"
          :series="userDataSeries"
        />
      </VCardText>
    </VCard>
    <VRow>
      <VCol cols="8">
        <VCard class="mb-6">
          <template #title>
            <div
              style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
            >
              <div class="mr-2 text-h5">
                AP信道负载排行
              </div>
              <div class="d-flex align-center">
                <BtnGroupSelector
                  v-model:value="networkType"
                  class="mr-2"
                  :options="WIFI_NETWORK_TYPE"
                />
                <DateSelector
                  v-model:current-value="date"
                  :items="DATE_FILTER_OPTIONS"
                  item-title="title"
                  item-value="value"
                />
              </div>
            </div>
          </template>
          <VDivider />
          <VDataTableServer
            :items="productsData.products"
            :headers="headers"
            :items-length="totalProduct"
            :no-data-text="t('NoData')"
          >
            <template #bottom>
              <TablePagination
                v-model:page="page"
                :items-per-page="10"
                :total-items="totalProduct"
              />
            </template>
          </VDataTableServer>
        </VCard>
      </VCol>
      <VCol cols="4">
        <VCard class="mb-6">
          <template #title>
            <div
              style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
            >
              <div class="mr-2 text-h5">
                AP无线重传率排行
              </div>
              <div class="d-flex align-center">
                <BtnGroupSelector
                  v-model:value="networkType"
                  class="mr-2"
                  :options="WIFI_NETWORK_TYPE"
                />
                <DateSelector
                  v-model:current-value="date"
                  :items="DATE_FILTER_OPTIONS"
                  item-title="title"
                  item-value="value"
                />
              </div>
            </div>
          </template>
          <VDivider />
          <VDataTableServer
            :items="productsData.products"
            :headers="table2Headers"
            :items-length="totalProduct"
            :no-data-text="t('NoData')"
          >
            <template #bottom>
              <TablePagination
                v-model:page="page"
                :items-per-page="10"
                :total-items="totalProduct"
              />
            </template>
          </VDataTableServer>
        </VCard>
      </VCol>
    </VRow>
    <VRow>
      <VCol cols="6">
        <VCard class="mb-6">
          <template #title>
            <div
              style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
            >
              <div class="mr-2 text-h5">
                AP无线干扰强度排行
              </div>
              <div class="d-flex align-center">
                <BtnGroupSelector
                  v-model:value="networkType"
                  class="mr-2"
                  :options="WIFI_NETWORK_TYPE"
                />
                <DateSelector
                  v-model:current-value="date"
                  :items="DATE_FILTER_OPTIONS"
                  item-title="title"
                  item-value="value"
                />
              </div>
            </div>
          </template>
          <VDivider />
          <VDataTableServer
            :items="productsData.products"
            :headers="table3Headers"
            :items-length="totalProduct"
            :no-data-text="t('NoData')"
          >
            <template #bottom>
              <TablePagination
                v-model:page="page"
                :items-per-page="10"
                :total-items="totalProduct"
              />
            </template>
          </VDataTableServer>
        </VCard>
      </VCol>
      <VCol cols="6">
        <VCard class="mb-6">
          <template #title>
            <div
              style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
            >
              <div class="mr-2 text-h5">
                AP弱信号占比排行
              </div>
              <div class="d-flex align-center">
                <BtnGroupSelector
                  v-model:value="networkType"
                  class="mr-2"
                  :options="WIFI_NETWORK_TYPE"
                />
                <DateSelector
                  v-model:current-value="date"
                  :items="DATE_FILTER_OPTIONS"
                  item-title="title"
                  item-value="value"
                />
              </div>
            </div>
          </template>
          <VDivider />
          <VDataTableServer
            :items="productsData.products"
            :headers="table2Headers"
            :items-length="totalProduct"
            :no-data-text="t('NoData')"
          >
            <template #bottom>
              <TablePagination
                v-model:page="page"
                :items-per-page="10"
                :total-items="totalProduct"
              />
            </template>
          </VDataTableServer>
        </VCard>
      </VCol>
    </VRow>
  </div>
</template>
