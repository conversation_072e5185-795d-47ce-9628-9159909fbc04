<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import AddDevice from '@/components/deviceList/addDevice.vue'
import MoreAdd from '@/components/deviceList/moreAdd.vue'
import ScanLocal from '@/components/deviceList/scanLocal.vue'

const { t } = useI18n()

const selectedRows = ref([])

// Data table options
const itemsPerPage = ref(10)
const page = ref(1)
const sortBy = ref()
const orderBy = ref()

// Update data table options
const updateOptions = (options: any) => {
  sortBy.value = options.sortBy[0]?.key
  orderBy.value = options.sortBy[0]?.order
}

// 表头
const headers = [
  { title: t('Device.List.Model'), key: 'no' },
  { title: t('Device.List.SerialNumber'), key: 'sn' },
  { title: t('Device.List.IPAddress'), key: 'ip' },
  { title: t('Device.List.MACAddress'), key: 'mac' },
  { title: t('Device.List.DiscoveryTime'), key: 'time' },
  { title: t('Device.List.Status'), key: 'status' },
  { title: t('Device.List.Actions'), key: 'actions', sortable: false },
]

// 数据
const productsData = ref({
  total: 3,
  products: [
    {
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: true,
    },
    {
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '',
      mac: '',
      status: false,
    },
    {
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: true,
    },
  ],
})

// 动态计算表格数据
const products = computed((): any[] => productsData.value.products)
const totalProduct = computed(() => productsData.value.total)

// 增加
const isDialogVisible = ref(false)

// 扫描
const isDialogVisibleScan = ref(false)

// 编辑
const isDialogVisibleEdit = ref(false)
</script>

<template>
  <div>
    <!-- 👉 products -->
    <VCard class="mb-6">
      <div class="d-flex flex-wrap gap-4 ma-6">
        <div class="d-flex align-center">
          <div class="cardTitle">
            {{ t('Device.List.DeviceDiscovery') }}
          </div>
        </div>
        <VSpacer />
        <div class="d-flex gap-4 flex-wrap align-center">
          <!-- <AppSelect v-model="itemsPerPage" :items="[5, 10, 20, 25, 50]" /> -->
          <VDialog
            v-model="isDialogVisibleScan"
            width="80%"
          >
            <!-- Activator -->
            <template #activator="{ props }">
              <VBtn
                color="success"
                variant="tonal"
                v-bind="props"
              >
                <VIcon icon="tabler-scan" />
                {{ t('Device.List.ScanLocalNetwork') }}
              </VBtn>
            </template>

            <!-- Dialog close btn -->
            <DialogCloseBtn @click="isDialogVisibleScan = !isDialogVisibleScan" />

            <!-- Dialog Content -->
            <VCard :title="t('Device.List.ScanLocalNetwork')">
              <ScanLocal @close="isDialogVisibleScan = false" />
            </VCard>
          </VDialog>

          <VDialog
            v-model="isDialogVisible"
            width="80%"
          >
            <!-- Activator -->
            <template #activator="{ props }">
              <VBtn
                v-bind="props"
                color="primary"
                prepend-icon="tabler-plus"
              >
                {{ t('Device.List.BatchAddDevices') }}
              </VBtn>
            </template>

            <!-- Dialog close btn -->
            <DialogCloseBtn @click="isDialogVisible = !isDialogVisible" />

            <!-- Dialog Content -->
            <VCard :title="t('Device.List.BatchAddDevices')">
              <MoreAdd @close="isDialogVisible = false" />
            </VCard>
          </VDialog>
        </div>
      </div>
      <VDivider class="mt-4" />
      <!-- 👉 Datatable  -->
      <VDataTableServer
        v-model:items-per-page="itemsPerPage"
        v-model:model-value="selectedRows"
        v-model:page="page"
        :headers="headers"
        :items="productsData.products"
        :items-length="totalProduct"
        :no-data-text="t('NoData')"
        class="text-no-wrap"
        show-select
        @update:options="updateOptions"
      >
        <!-- category -->
        <template #item.ip="{ item }">
          {{ item.ip || '--' }}
        </template>
        <!-- stock -->
        <template #item.mac="{ item }">
          {{ item.mac || '--' }}
        </template>
        <!-- status -->
        <template #item.status="{ item }">
          <VChip
            v-if="item.status"
            label
            color="success"
            size="small"
          >
            {{ t('Device.List.Online') }}
          </VChip>
          <VChip
            v-else
            label
            color="error"
            size="small"
          >
            {{ t('Device.List.Offline') }}
          </VChip>
        </template>

        <!-- Actions -->
        <template #item.actions>
          <div class="flexBox">
            <VDialog
              v-model="isDialogVisibleEdit"
              width="500"
            >
              <!-- Activator -->
              <template #activator="{ props }">
                <VBtn
                  variant="text"
                  v-bind="props"
                >
                  {{ t('Device.List.AddDevice') }}
                </VBtn>
              </template>

              <!-- Dialog close btn -->
              <DialogCloseBtn @click="isDialogVisibleEdit = !isDialogVisibleEdit" />

              <!-- Dialog Content -->
              <VCard :title="t('Device.List.AddDevice')">
                <AddDevice @close="isDialogVisibleEdit = false" />
              </VCard>
            </VDialog>
          </div>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalProduct"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </div>
</template>

<style scoped>
.flexBox {
  display: flex;
  align-items: center;
}

.cardTitle {
  font-family: "PingFang SC", serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 28px;
}
</style>
