<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { $post } from '@/utils/api'
import { isValidEmail, isValidPhoneNumber, md5Hash } from '@/utils/tool'
import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
import { passwordValidator, requiredValidator } from '@core/utils/validators'
import authV2LoginIllustrationBorderedDark from '@images/pages/auth-v2-login-illustration-bordered-dark.png'
import authV2LoginIllustrationBorderedLight from '@images/pages/auth-v2-login-illustration-bordered-light.png'
import authV2LoginIllustrationDark from '@images/pages/auth-v2-login-illustration-dark.png'
import authV2LoginIllustrationLight from '@images/pages/auth-v2-login-illustration-light.png'
import authV2MaskDark from '@images/pages/misc-mask-dark.png'
import authV2MaskLight from '@images/pages/misc-mask-light.png'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'

const router = useRouter()

definePage({
  meta: {
    layout: 'blank',
    public: true,
  },
})

const form = ref({
  systemUserName: '',
  systemPassword: '',
  smsCode: '',
  remember: false,
})

const formRef = ref()
const smsLoading = ref(false)
const smsCountdown = ref(0)
let smsTimer: any = null

const validateRules = {
  systemUserName: [
    (v: string) => requiredValidator(v, '请输入手机号/邮箱/用户名'),

    // 可选：如需邮箱格式校验可加 emailValidator
  ],
  systemPassword: [
    (v: string) => requiredValidator(v, '请输入密码'),
    (v: string) => passwordValidator(v),
  ],
  smsCode: [
    (v: string) => requiredValidator(v, '请输入验证码'),
  ],
}

const getSmsCode = async () => {
  if (!form.value.systemUserName) {
    ElMessage.error('请先输入手机号/邮箱/用户名')

    return
  }
  const phone = form.value.systemUserName
  let msgUrl = ''
  let data = {

  }
  if (isValidPhoneNumber(phone)) {
    msgUrl = '/auth/telCode'
    data = {
      tel: phone,
    }
  }
  if (isValidEmail(phone)) {
    msgUrl = '/auth/emailCode'
    data = {
      email: phone,
    }
  }
  smsLoading.value = true

  const res = await $post(msgUrl, data)

  console.log(res)
  setTimeout(() => {
    smsLoading.value = false
    smsCountdown.value = 60
    smsTimer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(smsTimer)
        smsTimer = null
      }
    }, 1000)
  }, 1000)
}

onMounted(() => {
  if (sessionStorage.getItem('remember') === '1') {
    form.value.systemUserName = sessionStorage.getItem('username') || ''
    form.value.systemPassword = sessionStorage.getItem('password') || ''
    form.value.remember = true
  }
  else {
    form.value.remember = false
  }
})

const isPasswordVisible = ref(false)

const authThemeImg = useGenerateImageVariant(
  authV2LoginIllustrationLight,
  authV2LoginIllustrationDark,
  authV2LoginIllustrationBorderedLight,
  authV2LoginIllustrationBorderedDark,
  true)

const authThemeMask = useGenerateImageVariant(authV2MaskLight, authV2MaskDark)

const sendRegister = async () => {
  // 校验表单
  const valid = await formRef.value?.validate?.()
  if (!valid)
    return
  const { systemUserName, systemPassword, smsCode, remember } = form.value

  console.log('发送注册请求:', remember)

  // if(!remember){
  //   ElMessage.error('请勾选我同意服务条款和隐私政策')
  //   return
  // }

  const data: any = {
    username: systemUserName,
    password: md5Hash(systemPassword),
    code: smsCode,
  }

  const res: any = await $post('/auth/signUp', data)
  if (res.msg === 'success') {
    ElMessage.success('注册成功')
    router.push('/login')
  }
}
</script>

<template>
  <a href="javascript:void(0)">
    <div class="auth-logo d-flex align-center gap-x-3">
      <VNodeRenderer :nodes="themeConfig.app.logo" />
      <h1 class="auth-title">{{ themeConfig.app.title }}</h1>
    </div>
  </a>

  <VRow
    class="auth-wrapper bg-surface"
    no-gutters
  >
    <VCol
      class="d-none d-md-flex"
      md="8"
    >
      <div class="position-relative bg-background w-100 me-0">
        <div
          class="d-flex align-center justify-center w-100 h-100"
          style="padding-inline: 6.25rem;"
        >
          <VImg
            :src="authThemeImg"
            class="auth-illustration mt-16 mb-2"
            max-width="613"
          />
        </div>

        <img
          :src="authThemeMask"
          alt="auth-footer-mask"
          class="auth-footer-mask flip-in-rtl"
          height="280"
          width="100"
        >
      </div>
    </VCol>

    <VCol
      class="auth-card-v2 d-flex align-center justify-center"
      cols="12"
      md="4"
    >
      <VCard
        :max-width="500"
        class="mt-12 mt-sm-0 pa-6"
        flat
      >
        <VCardText>
          <h4 class="text-h4 mb-1">
            立即加入 {{ $t('Someone') }} {{ $t('CommercialNetwork') }}
          </h4>
          <p class="mb-0">
            告别繁琐，让管理网络成为一种乐趣！
          </p>
        </VCardText>
        <VCardText>
          <VForm ref="formRef">
            <VRow>
              <!-- email -->
              <VCol cols="12">
                <AppTextField
                  v-model="form.systemUserName"
                  :rules="validateRules.systemUserName"
                  autofocus
                  label="手机号/邮箱/ 用户名"
                  placeholder="请输入手机号或邮箱或用户名"
                  type="email"
                />
              </VCol>
              <!-- password -->
              <VCol cols="12">
                <AppTextField
                  v-model="form.systemPassword"
                  :rules="validateRules.systemPassword"
                  :append-inner-icon="isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  autocomplete="password"
                  label="密码"
                  placeholder="请输入密码"
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />
              </VCol>
              <VCol cols="12">
                <div class="d-flex align-center mb-4">
                  <AppTextField
                    v-model="form.smsCode"
                    :rules="validateRules.smsCode"
                    label="验证码"
                    placeholder="请输入验证码"
                  >
                    <template #append>
                      <VBtn
                        color="primary"
                        :loading="smsLoading"
                        :disabled="smsCountdown > 0"
                        style="min-inline-size: 120px;"
                        @click="getSmsCode"
                      >
                        {{ smsCountdown > 0 ? `${smsCountdown}s后重试` : '获取验证码' }}
                      </VBtn>
                    </template>
                  </AppTextField>
                </div>

                <div class="d-flex align-center flex-wrap justify-space-between my-6">
                  <VCheckbox
                    v-model="form.remember"
                    label="我同意服务条款和隐私政策"
                  />
                </div>
                <VBtn
                  block
                  @click="sendRegister"
                >
                  注册
                </VBtn>
              </VCol>

              <VCol
                class="d-flex align-center justify-center"
                cols="12"
              >
                <span class="mx-4">已有账户？<span
                  class="text-primary"
                  @click="router.push('/login')"
                >立即登录</span></span>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth";
</style>
