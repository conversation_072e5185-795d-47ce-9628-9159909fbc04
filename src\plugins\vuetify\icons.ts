import type { IconAliases, IconProps } from 'vuetify'
import TablerAcManageCustom from '../iconify/icons/tabler-ac-manage-custom.vue'
import TablerApGroupCustom from '../iconify/icons/tabler-ap-group-custom.vue'
import TablerDeviceAddCustom from '../iconify/icons/tabler-device-add-custom.vue'
import TablerDownloadCustom from '../iconify/icons/tabler-download-custom.vue'
import TablerEventCustom from '../iconify/icons/tabler-event-custom.vue'
import TablerFileTextCustom from '../iconify/icons/tabler-file-text-custom.vue'
import TablerGlobeCustom from '../iconify/icons/tabler-globe-custom.vue'
import TablerListCustom from '../iconify/icons/tabler-list-custom.vue'
import TablerMemberCustom from '../iconify/icons/tabler-member-custom.vue'
import TablerMonitorCustom from '../iconify/icons/tabler-monitor-custom.vue'
import TablerNetworkCustom from '../iconify/icons/tabler-network-custom.vue'
import TablerNetworkOverviewCustom from '../iconify/icons/tabler-network-overview-custom.vue'
import TablerNetworkStatsCustom from '../iconify/icons/tabler-network-stats-custom.vue'
import TablerOverviewCustom from '../iconify/icons/tabler-overview-custom.vue'
import TablerRoleCustom from '../iconify/icons/tabler-role-custom.vue'
import TablerScanCustom from '../iconify/icons/tabler-scan-custom.vue'
import TablerSettingsCustom from '../iconify/icons/tabler-settings-custom.vue'
import TablerStatsCustom from '../iconify/icons/tabler-stats-custom.vue'
import TablerTopologyCustom from '../iconify/icons/tabler-topology-custom.vue'
import TablerVlanCustom from '../iconify/icons/tabler-vlan-custom.vue'
import TablerWifiCustom from '../iconify/icons/tabler-wifi-custom.vue'

import checkboxChecked from '@images/svg/checkbox-checked.svg'
import checkboxIndeterminate from '@images/svg/checkbox-indeterminate.svg'
import checkboxUnchecked from '@images/svg/checkbox-unchecked.svg'
import radioChecked from '@images/svg/radio-checked.svg'
import radioUnchecked from '@images/svg/radio-unchecked.svg'

const customIcons: Record<string, unknown> = {
  'mdi-checkbox-blank-outline': checkboxUnchecked,
  'mdi-checkbox-marked': checkboxChecked,
  'mdi-minus-box': checkboxIndeterminate,
  'mdi-radiobox-marked': radioChecked,
  'mdi-radiobox-blank': radioUnchecked,
  'tabler-file-text-custom': TablerFileTextCustom,
  'tabler-download-custom': TablerDownloadCustom,
  'tabler-list-custom': TablerListCustom,
  'tabler-monitor-custom': TablerMonitorCustom,
  'tabler-globe-custom': TablerGlobeCustom,
  'tabler-settings-custom': TablerSettingsCustom,
  'tabler-topology-custom': TablerTopologyCustom,
  'tabler-network-custom': TablerNetworkCustom,
  'tabler-event-custom': TablerEventCustom,
  'tabler-wifi-custom': TablerWifiCustom,
  'tabler-scan-custom': TablerScanCustom,
  'tabler-stats-custom': TablerStatsCustom,
  'tabler-overview-custom': TablerOverviewCustom,
  'tabler-device-add-custom': TablerDeviceAddCustom,
  'tabler-member-custom': TablerMemberCustom,
  'tabler-role-custom': TablerRoleCustom,
  'tabler-network-stats-custom': TablerNetworkStatsCustom,
  'tabler-network-overview-custom': TablerNetworkOverviewCustom,
  'tabler-ap-group-custom': TablerApGroupCustom,
  'tabler-ac-manage-custom': TablerAcManageCustom,
  'tabler-vlan-custom': TablerVlanCustom,
}

const aliases: Partial<IconAliases> = {
  calendar: 'tabler-calendar',
  collapse: 'tabler-chevron-up',
  complete: 'tabler-check',
  cancel: 'tabler-x',
  close: 'tabler-x',
  delete: 'tabler-circle-x-filled',
  clear: 'tabler-circle-x',
  success: 'tabler-circle-check',
  info: 'tabler-info-circle',
  warning: 'tabler-alert-triangle',
  error: 'tabler-alert-circle',
  prev: 'tabler-chevron-left',
  ratingEmpty: 'tabler-star',
  ratingFull: 'tabler-star-filled',
  ratingHalf: 'tabler-star-half-filled',
  next: 'tabler-chevron-right',
  delimiter: 'tabler-circle',
  sort: 'tabler-arrow-up',
  expand: 'tabler-chevron-down',
  menu: 'tabler-menu-2',
  subgroup: 'tabler-caret-down',
  dropdown: 'tabler-chevron-down',
  edit: 'tabler-pencil',
  loading: 'tabler-refresh',
  first: 'tabler-player-skip-back',
  last: 'tabler-player-skip-forward',
  unfold: 'tabler-arrows-move-vertical',
  file: 'tabler-paperclip',
  plus: 'tabler-plus',
  minus: 'tabler-minus',
  sortAsc: 'tabler-arrow-up',
  sortDesc: 'tabler-arrow-down',
}

export const iconify = {
  component: (props: IconProps) => {
    // Load custom SVG directly instead of going through icon component
    if (typeof props.icon === 'string') {
      const iconComponent = customIcons[props.icon]

      if (iconComponent)
        return h(iconComponent)
    }

    return h(
      props.tag,
      {
        ...props,

        // As we are using class based icons
        class: [props.icon],

        // Remove used props from DOM rendering
        tag: undefined,
        icon: undefined,
      },
    )
  },
}

export const icons = {
  defaultSet: 'iconify',
  aliases,
  sets: {
    iconify,
  },
}
