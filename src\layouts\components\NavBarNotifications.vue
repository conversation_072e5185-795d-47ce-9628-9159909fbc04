<script lang="ts" setup>
const notifications = ref<any[]>([])

const removeNotification = (notificationId: number) => {
  $post('', {
    requestType: 524,
    data: {
      event_id: notificationId,
      status: '1',
    },
  }).then(result => {
    if (result.err_code === 0)
      getEventList()
  })
}

const markRead = (notificationId: number) => {
  $post('', {
    requestType: 538,
    data: {
      event_id: notificationId,
      isRead: '1',
    },
  }).then(result => {
    if (result.err_code === 0)
      getEventList()
  })
}

const handleNotificationClick = (notification: any) => {
  $post('', {
    requestType: 538,
    data: {
      event_id: notification.event_id,
      isRead: '1',
    },
  }).then(result => {
    if (result.err_code === 0)
      getEventList()
  })
}

const getEventList = () => {
  $post('', { requestType: 522 }).then(result => {
    if (result.err_code === 0) {
      const list = result.info.ap_events

      notifications.value = list
    }
  })
}

onMounted(async () => {
  // getEventList()
})
</script>

<template>
  <Notifications
    :notifications="notifications"
    @ignore="removeNotification"
    @read="markRead"
    @click:notification="handleNotificationClick"
  />
</template>
