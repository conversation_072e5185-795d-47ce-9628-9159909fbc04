<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import clientIcon from '@images/pages/icon-client-dashboard.png'
import offlineAp from '@images/pages/icon-offline-ap-dashboard.png'
import onlineAp from '@images/pages/icon-online-ap-dashboard.png'

const { t } = useI18n()

const logisticData = ref([
  { icon: onlineAp, color: 'primary', title: t('OnlineAP'), value: 0, isHover: false, textBg: '', borderCls: 'border-b-fill-warning' },
  { icon: offlineAp, color: 'error', title: t('OfflineAP'), value: 0, isHover: false, textBg: 'bg-error', borderCls: 'border-b-fill-warning' },
  { icon: clientIcon, color: 'info', title: t('TerminalCount'), value: 0, isHover: false, textBg: 'bg-error', borderCls: 'border-b-fill-info' },
])

onMounted(() => {
  getAllInfo()
  getClientInfo()
})

const getAllInfo = async () => {
  const data: any = {
    requestType: 509,
  }

  const res: any = await $post('', data)

  if (res.msg === 'success') {
    logisticData.value[0].value = res.info.online || 0
    logisticData.value[1].value = res.info.offline || 0
    logisticData.value[2].value = res.info.total_clients || 0
  }
  else { /* empty */ }
}

const getClientInfo = async () => {
  const data: any = {
    requestType: 209,
  }

  const res: any = await $post('', data)

  console.log(res)
  if (res.msg === 'success') {
    logisticData.value[2].value = res.info.dhcpClinet?.length || 0 + res.info.wireless?.wifi_2G?.length || 0 + res.info.wireless?.wifi_5G?.length || 0
  }
  else { /* empty */ }
}
</script>

<template>
  <VRow>
    <VCol
      v-for="(data, index) in logisticData"
      :key="index"
      md="4"
    >
      <div>
        <VCard
          class="logistics-card-statistics cursor-pointer border-b-md"
          :class="[data.borderCls]"
          style="border: 0;"
          @mouseenter="data.isHover = true"
          @mouseleave="data.isHover = false"
        >
          <VCardText>
            <div class="d-flex align-center gap-x-4 mb-1">
              <VAvatar
                variant="tonal"
                :color="data.color"
                rounded
              >
                <img
                  :src="data.icon"
                  style="block-size: 28px;inline-size: 28px;"
                  alt=""
                >
              </VAvatar>
              <h4 class="text-h4">
                {{ data.value }} {{ t('UnitDevice') }}
              </h4>
            </div>
            <div class="text-subtitle-1 mt-4">
              {{ data.title }}
            </div>
          </VCardText>
        </VCard>
      </div>
    </VCol>
  </VRow>
</template>

<style lang="scss" scoped>
@use "@core/scss/base/mixins" as mixins;

.border-b-fill-primary {
  border-color: rgba($color: var(--v-theme-primary), $alpha: 100%) !important;
}

.border-b-fill-warning {
  border-color: rgba($color: var(--v-theme-warning), $alpha: 100%) !important;
}

.border-b-fill-info {
  border-color: rgba($color: var(--v-theme-info), $alpha: 100%) !important;
}

.statusBox {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--Border-Radius-border-radius-sm, 4px);
  background-color: rgba(var(--v-theme-on-surface), var(--v-activated-opacity));
  block-size: 24px;
  color: var(--Light-Text-Primary, text-primary);
  font-family: "PingFang HK";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px !important;
  font-style: normal;
  font-weight: 500;
  inline-size: 56px;
}

.descText {
  color: var(--v-theme-on-surface);
  font-family: "PingFang HK";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 15px !important;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
}

.logistics-card-statistics {
  border-block-end-style: solid;
  border-block-end-width: 2px;

  &:hover {
    border-block-end-width: 3px;
    margin-block-end: -1px;

    @include mixins.elevation(8);

    transition: all 0.1s ease-out;
  }
}

.skin--bordered {
  .logistics-card-statistics {
    border-block-end-width: 2px;

    &:hover {
      border-block-end-width: 3px;
      margin-block-end: -2px;
      transition: all 0.1s ease-out;
    }
  }
}
</style>
