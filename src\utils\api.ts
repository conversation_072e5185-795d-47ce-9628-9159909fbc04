import { ElMessage } from 'element-plus'
import { ofetch } from 'ofetch'
import { getI18n } from '@/plugins/i18n'

// 获取 i18n 实例的 t 函数
const getT = () => {
  const i18n = getI18n()

  return i18n.global.t
}

// 定义基础响应类型
interface ApiResponse<T = any> {
  err_code: number
  err_message: string
}

// 创建自定义 fetch 实例
const apiFetcher = ofetch.create({
  // 基础配置
  baseURL: 'https://s.acap.86.ltd',
  headers: {
    'Content-Type': 'application/json',
  },
  async onRequest({ options }) {
    // 在每次请求时动态获取 token
    const token = sessionStorage.getItem('token') || ''

    options.headers = {
      ...options.headers,
      Authorization: token,
    }

    // 如果body是FormData类型，不设置Content-Type，让浏览器自动处理
    if (options.body instanceof FormData)
      delete options.headers['Content-Type']
    else if (options.body && typeof options.body !== 'string')
      options.body = JSON.stringify(options.body)
  },
  async onResponse({ response }) {
    // console.log(response, '99999')
  },

  // 错误处理
  async onResponseError({ response }) {
    const t = getT()

    // console.log(response, 'onResponseError')
    if (response.status == 401) {
      ElMessage.error(t('ApiErrors.PleaseLogin'))
      window.location.replace('/login')
    }
    if (response.status == 204) {
    }
    if (response.status == 400)
      ElMessage.error(response._data.msg || '请求出错')
  },
})

export function $api() {
  return {
    err_code: 0,
  }
}

// 封装 POST 请求
export function $post<T = any>(url: string, body?: Record<string, any>) {
  return apiFetcher<T>(url, {
    method: 'POST',
    body,
  })
}

// 封装GET 请求
export function $get<T = any>(url: string, params?: Record<string, any>) {
  return apiFetcher<T>(url, {
    method: 'GET',
    params,
  })
}

// 封装 PUT 请求
export function $put<T = any>(url: string, body?: Record<string, any>) {
  return apiFetcher<T>(url, {
    method: 'PUT',
    body,
  })
}

// 封装 DELETE 请求
export function $delete<T = any>(url: string, body?: Record<string, any>) {
  return apiFetcher<T>(url, {
    method: 'DELETE',
    body,
  })
}

// 封装文件上传请求
export function $file<T = any>(url: string, formData: FormData) {
  return apiFetcher<T>(url, {
    method: 'POST',
    body: formData,
  })
}
