<script lang="ts" setup>
import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
import authV2LoginIllustrationBorderedDark from '@images/pages/auth-v2-login-illustration-bordered-dark.png'
import authV2LoginIllustrationBorderedLight from '@images/pages/auth-v2-login-illustration-bordered-light.png'
import authV2LoginIllustrationDark from '@images/pages/auth-v2-login-illustration-dark.png'
import authV2LoginIllustrationLight from '@images/pages/auth-v2-login-illustration-light.png'
import authV2MaskDark from '@images/pages/misc-mask-dark.png'
import authV2MaskLight from '@images/pages/misc-mask-light.png'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'

const router = useRouter()

definePage({
  meta: {
    layout: 'blank',
    public: true,
  },
})

const form = ref({
  systemUserName: 'admin',
  systemPassword: '',
  remember: false,
})

onMounted(() => {
  if (sessionStorage.getItem('remember') === '1') {
    form.value.systemUserName = sessionStorage.getItem('username')
    form.value.systemPassword = sessionStorage.getItem('password')
    form.value.remember = true
  }
  else {
    form.value.remember = false
  }
})

const isPasswordVisible = ref(false)

const authThemeImg = useGenerateImageVariant(
  authV2LoginIllustrationLight,
  authV2LoginIllustrationDark,
  authV2LoginIllustrationBorderedLight,
  authV2LoginIllustrationBorderedDark,
  true)

const authThemeMask = useGenerateImageVariant(authV2MaskLight, authV2MaskDark)

const sendLogin = async () => {
  const { systemUserName, systemPassword, remember } = form.value

  const data: any = {
    requestType: 305,
    data: {
      systemUserName,
      systemPassword,
    },
  }

  const res: any = await $post('', data)

  if (res.msg === 'success') {
    sessionStorage.setItem('captcha', res.info.captcha)
    sessionStorage.setItem('username', systemUserName)
    if (remember === true) {
      sessionStorage.setItem('password', systemPassword)
      sessionStorage.setItem('remember', '1')
    }
    else {
      sessionStorage.setItem('remember', '0')
    }
    router.push('/')
  }
}
</script>

<template>
  <a href="javascript:void(0)">
    <div class="auth-logo d-flex align-center gap-x-3">
      <VNodeRenderer :nodes="themeConfig.app.logo" />
      <h1 class="auth-title">{{ themeConfig.app.title }}</h1>
    </div>
  </a>

  <VRow
    class="auth-wrapper bg-surface"
    no-gutters
  >
    <VCol
      class="d-none d-md-flex"
      md="8"
    >
      <div class="position-relative bg-background w-100 me-0">
        <div
          class="d-flex align-center justify-center w-100 h-100"
          style="padding-inline: 6.25rem;"
        >
          <VImg
            :src="authThemeImg"
            class="auth-illustration mt-16 mb-2"
            max-width="613"
          />
        </div>

        <img
          :src="authThemeMask"
          alt="auth-footer-mask"
          class="auth-footer-mask flip-in-rtl"
          height="280"
          width="100"
        >
      </div>
    </VCol>

    <VCol
      class="auth-card-v2 d-flex align-center justify-center"
      cols="12"
      md="4"
    >
      <VCard
        :max-width="500"
        class="mt-12 mt-sm-0 pa-6"
        flat
      >
        <VCardText>
          <h4 class="text-h4 mb-1">
            欢迎使用 {{ themeConfig.app.title }}
          </h4>
          <p class="mb-0">
            化繁为简，轻松掌控您的网络系统
          </p>
        </VCardText>
        <VCardText>
          <VForm @submit.prevent="() => {}">
            <VRow>
              <!-- email -->
              <VCol cols="12">
                <AppTextField
                  v-model="form.systemUserName"
                  autofocus
                  label="手机号/邮箱/ 用户名"
                  placeholder="请输入手机号或邮箱或用户名"
                  readonly
                  type="email"
                />
              </VCol>
              <!-- password -->
              <VCol cols="12">
                <AppTextField
                  v-model="form.systemPassword"
                  :append-inner-icon="isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  autocomplete="password"
                  label="密码"
                  placeholder="请输入密码"
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />
                <div class="d-flex align-center flex-wrap justify-space-between my-6">
                  <VCheckbox
                    v-model="form.remember"
                    label="记住账号"
                  />
                  <span @click="router.push('/role/codeLogin')">验证码登录</span>
                </div>
                <VBtn
                  block
                  @click="sendLogin"
                >
                  登录
                </VBtn>
              </VCol>

              <VCol
                class="d-flex align-center justify-center"
                cols="12"
              >
                <span class="mx-4">还没有账户？<span
                  class="text-primary"
                  @click="router.push('/role/register')"
                >立即注册</span></span>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth";
</style>
