<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showRepeatNewPassword = ref(false)

const formData = reactive({
  currentUserName: 'admin',
  systemUserName: '',
  currentPassword: '',
  systemPassword: '',
  repeatSystemPassword: '',
})

const safeForm = ref()

const usr_cloud_onoff = ref('1')

// 新增AC开关
const usrAcapOnoff = ref('1')

const save = () => {
  safeForm.value.validate().then((result: any) => {
    if (result.valid) {
      if (formData.systemPassword !== formData.repeatSystemPassword) {
        ElMessage.error(t('SystemConfig.Security.PasswordMismatch'))

        return
      }
      $api('', {
        requestType: 306,
        data: {
          currentUserName: formData.currentUserName,
          systemUserName: formData.currentUserName,
          currentPassword: formData.currentPassword,
          systemPassword: formData.systemPassword,
        },
      }).then(res => {
        if (res.err_code == 0) {
          $api('', {
            requestType: 307,
          }).then(() => {
            ElMessage.success(t('SystemConfig.Security.SettingsSuccess'))
            sessionStorage.setItem('username', undefined)
            sessionStorage.setItem('captcha', undefined)
            window.location.reload()
          })
        }
        else if (res.err_code == -18) {
          ElMessage.error(t('SystemConfig.Security.IncorrectCredentials'))
        }
        else if (res.err_code == -21) {
          ElMessage.error(t('SystemConfig.Security.SamePassword'))
        }
        else {
          ElMessage.error(t('SystemConfig.Security.SettingsFailed'))
        }
      })
    }
  })
}

const getUsrInfo = () => {
  $api('', {
    requestType: 311,
    data: {},
  }).then(res => {
    if (res.msg === 'success') {
      usr_cloud_onoff.value = res.info.usr_cloud.usr_cloud_onoff

      // 新增AC开关赋值
      usrAcapOnoff.value = res.info.usr_cloud.usr_acap_onoff
    }
    else {
      ElMessage.error(t('SystemConfig.Security.SettingsFailed'))
    }
  })
}

const changeUsrCloud = () => {
  $api('', {
    requestType: 312,
    data: { usr_cloud_onoff: usr_cloud_onoff.value, usr_acap_onoff: usrAcapOnoff.value },
  }).then(res => {
    if (res.msg === 'success') {
      ElMessage.success(t('SystemConfig.Security.SettingsSuccess'))
      getUsrInfo()
    }
    else { ElMessage.error(t('SystemConfig.Security.SettingsFailed')) }
  })
}

const requiredValidator = (value: string) => {
  if (value === '')
    return t('NetworkConfig.LAN.Required')

  return true
}

onMounted(() => {
  getUsrInfo()
})
</script>

<template>
  <VForm ref="safeForm">
    <VCard
      class="mb-5"
      :title="t('SystemConfig.Security.LoginSettings')"
    >
      <VCardText>
        <VRow class="match-height mb-2">
          <VCol
            cols="12"
            md="6"
          >
            <div class="text-h6 mb-2">
              {{ t('SystemConfig.Security.Username') }}
            </div>
            <AppTextField
              v-model="formData.currentUserName"
              :placeholder="t('SystemConfig.Security.EnterUsername')"
              :rules="[requiredValidator]"
              :readonly="true"
            />
          </VCol>
        </VRow>
        <VRow class="match-height">
          <VCol
            cols="12"
            md="6"
          >
            <div class="text-h6 mb-2">
              {{ t('SystemConfig.Security.OriginalPassword') }}
            </div>
            <AppTextField
              v-model="formData.currentPassword"
              :placeholder="t('SystemConfig.Security.EnterOriginalPassword')"
              :append-inner-icon="showCurrentPassword ? 'tabler-eye-off' : 'tabler-eye'"
              :type="showCurrentPassword ? 'text' : 'password'"
              :rules="[requiredValidator]"
              @click:append-inner="showCurrentPassword = !showCurrentPassword"
            />
          </VCol>
        </VRow>
        <VRow class="match-height mb-2">
          <VCol
            cols="12"
            md="6"
          >
            <div class="text-h6 mb-2">
              {{ t('SystemConfig.Security.NewPassword') }}
            </div>
            <AppTextField
              v-model="formData.systemPassword"
              :placeholder="t('SystemConfig.Security.NewPasswordDesc')"
              :append-inner-icon="showNewPassword ? 'tabler-eye-off' : 'tabler-eye'"
              :type="showNewPassword ? 'text' : 'password'"
              :rules="[requiredValidator]"
              @click:append-inner="showNewPassword = !showNewPassword"
            />
          </VCol>
          <VCol
            cols="12"
            md="6"
          >
            <div class="text-h6 mb-2">
              {{ t('SystemConfig.Security.ConfirmNewPassword') }}
            </div>
            <AppTextField
              v-model="formData.repeatSystemPassword"
              :placeholder="t('SystemConfig.Security.PleaseConfirmNewPassword')"
              :append-inner-icon="showRepeatNewPassword ? 'tabler-eye-off' : 'tabler-eye'"
              :type="showRepeatNewPassword ? 'text' : 'password'"
              :rules="[requiredValidator]"
              @click:append-inner="showRepeatNewPassword = !showRepeatNewPassword"
            />
          </VCol>
        </VRow>
        <div class="d-flex justify-end">
          <VBtn
            color="primary"
            @click="save"
          >
            {{ t('SystemConfig.Security.SaveSettings') }}
          </VBtn>
        </div>
      </VCardText>
    </VCard>
  </VForm>
  <VCard
    class="mb-5"
    :title="t('SystemConfig.Tabs.SecurityManagement')"
  >
    <VCardText>
      <!-- 新增AC开关部分 -->
      <div class="mb-2 border pa-4 rounded d-flex align-center justify-space-between">
        <div>
          <div class="mb-1 text-h6">
            {{ t('SystemConfig.Security.USRAcap') }}
          </div>
          <div class="text-subtitle-2">
            {{ t('SystemConfig.Security.USRAcapDesc') }}
          </div>
        </div>
        <VSwitch
          v-model="usrAcapOnoff"
          true-value="1"
          false-value="0"
          @update:model-value="changeUsrCloud"
        />
      </div>
      <div class="mb-2 border pa-4 rounded d-flex align-center justify-space-between">
        <div>
          <div class="mb-1 text-h6">
            {{ t('SystemConfig.Security.USRCloud') }}
          </div>
          <div class="text-subtitle-2">
            {{ t('SystemConfig.Security.USRCloudDesc') }}
          </div>
        </div>
        <VSwitch
          v-model="usr_cloud_onoff"
          true-value="1"
          false-value="0"
          @update:model-value="changeUsrCloud"
        />
      </div>
    </VCardText>
  </VCard>
</template>
