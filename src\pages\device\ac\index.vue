<script setup lang="ts">
import { computed, ref } from 'vue'
import { router } from '@/plugins/1.router'

interface ACDevice {
  id: string
  name: string
  model: string
  sn: string
  ip: string
  mac: string
  version: string
  runTime: string
  onlineStatus: 'online' | 'offline'
}

// 表头配置
const headers = [
  { title: '名称', key: 'name', sortable: true },
  { title: '型号', key: 'model', sortable: true },
  { title: 'SN', key: 'sn', sortable: true },
  { title: 'IP', key: 'ip', sortable: true },
  { title: 'MAC', key: 'mac', sortable: true },
  { title: '固件版本', key: 'version', sortable: true },
  { title: '运行时间', key: 'runTime', sortable: true },
  { title: '在线状态', key: 'onlineStatus', sortable: false },
  { title: '操作', key: 'actions', sortable: false },
]

// 生成更多设备数据
const generateDevices = (): ACDevice[] => {
  const devices: ACDevice[] = []
  const models = ['AC200', 'AC300', 'AC400', 'AC500']
  const apModels = ['AP200', 'AP300', 'AP400', 'AP500']
  const versions = ['v2.7.4', 'v2.8.1', 'v3.0.2', 'v3.1.0']
  const statuses: ('online' | 'offline')[] = ['online', 'offline']

  for (let i = 1; i <= 26; i++) {
    const modelIndex = (i - 1) % models.length
    const apModelIndex = (i - 1) % apModels.length
    const versionIndex = (i - 1) % versions.length
    const statusIndex = Math.floor(Math.random() * 2)

    // 生成不同的IP地址
    const ipBase = Math.floor((i - 1) / 10) + 100
    const ipLast = ((i - 1) % 10) + 100

    // 生成不同的MAC地址
    const macSuffix = i.toString(16).padStart(2, '0').toUpperCase()

    // 生成不同的SN
    const snSuffix = (2628 + i - 1).toString()

    // 生成不同的运行时间
    const days = Math.floor(Math.random() * 30) + 1
    const hours = Math.floor(Math.random() * 24)
    const minutes = Math.floor(Math.random() * 60)

    devices.push({
      id: i.toString(),
      name: models[modelIndex],
      model: apModels[apModelIndex],
      sn: `30801524053000000${snSuffix}`,
      ip: `${ipBase}.${ipBase}.${ipBase}.${ipLast}`,
      mac: `AA:BB:CC:DD:EE:${macSuffix}`,
      version: versions[versionIndex],
      runTime: `${days.toString().padStart(2, '0')}d ${hours.toString().padStart(2, '0')}h ${minutes.toString().padStart(2, '0')}s`,
      onlineStatus: statuses[statusIndex],
    })
  }

  return devices
}

const deviceData = ref({
  total: 26,
  devices: generateDevices(),
})

const selectedDevices = ref<string[]>([])
const loading = ref(false)
const itemsPerPage = ref(10)
const page = ref(1)
const sortBy = ref<{ key: string; order?: 'asc' | 'desc' }[]>([])

// 抽屉状态管理
const isAddDrawerOpen = ref(false)
const isManageDrawerOpen = ref(false)
const currentTab = ref('manual')
const currentDevice = ref<ACDevice | null>(null)

// 返回所有设备数据，让VDataTable处理分页和排序
const devices = computed((): ACDevice[] => {
  return deviceData.value.devices
})

// 计算总数
const totalDevices = computed(() => deviceData.value.devices.length)

// 排序事件
const sortchange = (val: { key: string; order?: 'asc' | 'desc' }[]) => {
  sortBy.value = val
}

// 打开添加设备抽屉
const openAddDrawer = () => {
  // isAddDrawerOpen.value = true
  // currentTab.value = 'manual'
  router.push('/system/networkConfig')
}

// 关闭添加设备抽屉
const closeAddDrawer = () => {
  isAddDrawerOpen.value = false
}

// 打开设备管理抽屉
const openManageDrawer = (device: ACDevice) => {
  // currentDevice.value = device
  // isManageDrawerOpen.value = true
  router.push('/system/systemConfig')
}

// 关闭设备管理抽屉
const closeManageDrawer = () => {
  isManageDrawerOpen.value = false
  currentDevice.value = null
}

const handleAction = (action: string, deviceId: string) => {
  const device = devices.value.find(d => d.id === deviceId)

  switch (action) {
  case '设备管理':
    if (device)
      openManageDrawer(device)

    break
  case '运程配置':
    console.log('运程配置', deviceId)
    break
  case '重启设备':
    console.log('重启设备', deviceId)
    break
  case '删除设备':
    console.log('删除设备', deviceId)
    break
  default:
    console.log(`${action} for device ${deviceId}`)
  }
}

const addDevice = () => {
  openAddDrawer()
}
</script>

<template>
  <div class="ac-management-page">
    <!-- AC管理列表 -->
    <VCard class="device-table-card">
      <!-- 页面标题和操作按钮 -->
      <div class="d-flex flex-wrap gap-4 ma-6">
        <h2 class="page-title">
          AC管理
        </h2>
        <VSpacer />
        <VBtn
          color="primary"
          prepend-icon=""
          @click="addDevice"
        >
          +
          添加设备
        </VBtn>
      </div>
      <VDivider />
      <VDataTable
        v-model="selectedDevices"
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="devices"
        :loading="loading"
        show-select
        item-value="id"
        class="text-no-wrap"
        no-data-text="暂无数据"
        :sort-by="sortBy"
        @update:sort-by="sortchange"
      >
        <!-- IP列 -->
        <template #item.ip="{ item }">
          <a
            href="#"
            class="ip-link"
          >{{ item.ip }}</a>
        </template>

        <!-- 在线状态列 -->
        <template #item.onlineStatus="{ item }">
          <VChip
            :color="item.onlineStatus === 'online' ? 'success' : 'error'"
            label
            size="small"
          >
            {{ item.onlineStatus === 'online' ? '在线' : '离线' }}
          </VChip>
        </template>

        <!-- 操作列 -->
        <template #item.actions="{ item }">
          <VMenu>
            <template #activator="{ props }">
              <VBtn
                icon
                size="small"
                variant="text"
                v-bind="props"
              >
                <VIcon icon="tabler-dots-vertical" />
              </VBtn>
            </template>
            <VList>
              <VListItem @click="handleAction('运程配置', item.id)">
                <VListItemTitle>运程配置</VListItemTitle>
              </VListItem>
              <VListItem @click="handleAction('设备管理', item.id)">
                <VListItemTitle>设备管理</VListItemTitle>
              </VListItem>
              <VListItem @click="handleAction('重启设备', item.id)">
                <VListItemTitle>重启设备</VListItemTitle>
              </VListItem>
              <VListItem @click="handleAction('删除设备', item.id)">
                <VListItemTitle class="text-primary">
                  删除设备
                </VListItemTitle>
              </VListItem>
            </VList>
          </VMenu>
        </template>

        <!-- 底部分页 -->
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalDevices"
          />
        </template>
      </VDataTable>
    </VCard>

    <!-- 右侧添加设备抽屉 -->
    <VNavigationDrawer
      v-model="isAddDrawerOpen"
      location="end"
      temporary
      width="600"
      class="add-device-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            添加AC设备
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeAddDrawer"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-hidden">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="pa-4">
              <!-- 内容区域留空，但保持滚动功能 -->
            </div>
          </PerfectScrollbar>
        </div>

        <!-- 底部固定区域 -->
        <VDivider />
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-space-between">
          <div />
          <div>
            <VBtn
              class="me-4"
              color="secondary"
              variant="tonal"
              @click="closeAddDrawer"
            >
              取消
            </VBtn>
            <VBtn color="primary">
              添加设备
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- 右侧设备管理抽屉 -->
    <VNavigationDrawer
      v-model="isManageDrawerOpen"
      location="end"
      temporary
      width="600"
      class="manage-device-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            设备管理 - {{ currentDevice?.name }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeManageDrawer"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-hidden">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="pa-4">
              <!-- 内容区域留空，但保持滚动功能 -->
            </div>
          </PerfectScrollbar>
        </div>

        <!-- 底部固定区域 -->
        <VDivider />
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-space-between">
          <div>
            <VBtn
              color="error"
              variant="outlined"
            >
              删除设备
            </VBtn>
          </div>
          <div>
            <VBtn
              class="me-4"
              color="secondary"
              variant="tonal"
              @click="closeManageDrawer"
            >
              取消
            </VBtn>
            <VBtn color="primary">
              保存更改
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style scoped lang="scss">
.ac-management-page {
  block-size: 100%;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.device-table-card {
  .ip-link {
    color: rgb(var(--v-theme-primary));
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.add-device-drawer,
.manage-device-drawer {
  // AP-style form classes
  .h-38 {
    block-size: 38px;
    line-height: 38px;
  }

  .w-80 {
    inline-size: 80px;
  }

  .template-link {
    display: flex;
    align-items: center;
    color: rgb(var(--v-theme-primary));
    cursor: pointer;
    gap: 8px;

    .download-icon {
      color: rgb(var(--v-theme-primary));
    }

    .link-text {
      font-size: 14px;
      text-decoration: underline;
    }

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
