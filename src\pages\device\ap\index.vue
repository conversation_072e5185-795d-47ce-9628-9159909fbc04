<script lang="ts" setup>
import type { UploadFile, UploadFiles, UploadRawFile } from 'element-plus'
import { ElMessage, ElMessageBox, ElUpload, genFileId } from 'element-plus'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  BAND_WIDTH_2G,
  CHANNEL_ARR_2G,
  CHANNEL_ARR_5G,
  COUNTRY_OPTIONS,
  NET_TYPE,
  PROTOCOL_2G,
  TX_POWER_2G,
  TX_POWER_5G,
} from '@/utils/constants'
import BtnGroupSelector from '@/components/network/BtnGroupSelector.vue'

const { t } = useI18n()

// 侧边栏
const drawer = ref(false)
const deviceInfoTable: any = ref([])
const selectedBandwidth = ref('20M')
const HTMode = ref('HT20')
const requestId = ref('')

// 当前激活的标签页
const currentTab = ref(0)

// 用于控制标签页内容的延迟渲染
const tabContentLoaded = ref({
  0: true, // 设备信息默认加载
  1: false, // 无线状态延迟加载
  2: false, // 工作模式延迟加载
})

// 监听标签页切换，延迟加载内容
watch(currentTab, async newTab => {
  if (!tabContentLoaded.value[newTab]) {
    // 使用nextTick确保标签页切换动画完成后再渲染内容
    await nextTick()
    setTimeout(() => {
      tabContentLoaded.value[newTab] = true
    }, 50) // 50ms延迟，让切换动画更流畅
  }
})

const selectedRows = ref<any[]>([])

const itemsPerPage = ref(10)
const page = ref(1)

// 表头
const headers = [
  { title: t('Device.AP.Name'), key: 'name', sortable: false },
  { title: t('Device.AP.Model'), key: 'model', sortable: false },
  { title: t('Device.AP.SerialNumber'), key: 'sn', sortable: true },
  { title: t('Device.AP.IPAddress'), key: 'ip', sortable: false },
  { title: t('Device.AP.MACAddress'), key: 'mac', sortable: false },
  { title: t('Device.AP.FirmwareVersion'), key: 'version', sortable: false },
  { title: t('Device.AP.RunningTime'), key: 'uptime', sortable: false },
  { title: t('Device.AP.Status'), key: 'onoff', sortable: false },
  { title: t('Device.AP.Actions'), key: 'actions', sortable: false },
]

// 子网掩码选项列表
const subnetMaskOptions = [
  // A类网络常用
  { label: '*********/8', value: '*********' },
  { label: '***********/9', value: '***********' },
  { label: '***********/10', value: '***********' },
  { label: '***********/11', value: '***********' },
  { label: '***********/12', value: '***********' },
  { label: '***********/13', value: '***********' },
  { label: '***********/14', value: '***********' },
  { label: '***********/15', value: '***********' },

  // B类网络常用
  { label: '***********/16', value: '***********' },
  { label: '*************/17', value: '*************' },
  { label: '255.255.192.0/18', value: '255.255.192.0' },
  { label: '255.255.224.0/19', value: '255.255.224.0' },
  { label: '255.255.240.0/20', value: '255.255.240.0' },
  { label: '255.255.248.0/21', value: '255.255.248.0' },
  { label: '255.255.252.0/22', value: '255.255.252.0' },
  { label: '255.255.254.0/23', value: '255.255.254.0' },

  // C类网络常用
  { label: '255.255.255.0/24', value: '255.255.255.0' },
  { label: '255.255.255.128/25', value: '255.255.255.128' },
  { label: '255.255.255.192/26', value: '255.255.255.192' },
  { label: '255.255.255.224/27', value: '255.255.255.224' },
  { label: '255.255.255.240/28', value: '255.255.255.240' },
  { label: '255.255.255.248/29', value: '255.255.255.248' },
  { label: '255.255.255.252/30', value: '255.255.255.252' },

  // 特殊用途
  { label: '255.255.255.254/31', value: '255.255.255.254' },
  { label: '255.255.255.255/32', value: '255.255.255.255' },
]

const operation = ref()

// No longer reset selections when operation changes
const operationChange = () => {
  // Keep selections intact
}

const apHandlers = ref([{
  label: t('Device.AP.Operations.Restart'),
  value: 0,
}, {
  label: t('Device.AP.Operations.FactoryReset'),
  value: 1,
}, {
  label: t('Device.AP.Operations.Upgrade'),
  value: 2,
}, {
  label: t('Device.AP.Operations.Delete'),
  value: 3,
}, {
  label: t('Device.AP.Operations.BlinkLED'),
  value: 4,
}, {
  label: t('Device.AP.Operations.ExportSN'),
  value: 5,
}])

// 数据
const apData = ref({
  total: 0,
  apList: [] as any[],
})

const selectedModel = ref<string | null>(null)
const modelList = ref([] as { label: string; value?: string }[])

// 新增：获取型号列表
const getModelList = () => {
  $get('/v1/apModel', {}).then(res => {
    if (res.msg === 'success' && Array.isArray(res.result)) {
      modelList.value = res.result.map((item: any) => ({
        label: item || '',
        value: item || '',
      }))
      modelList.value.unshift({
        label: t('Device.AP.All'),
        value: '',
      })

      // 初始不选中任何型号
      selectedModel.value = null
    }
  })
}

const ledMode = ref('1')

const resetAfterUpdate = ref('0')

const uploader = ref()
const fileList = ref()

const handleChange = (uploadFile: UploadFile) => {
  fileList.value = [uploadFile]
}

const handleExceed = (files: UploadFiles) => {
  uploader.value!.clearFiles()

  const file = files[0] as UploadRawFile

  file.uid = genFileId()
  uploader.value!.handleStart(file)
}

// 排序相关变量
const sortBy = ref<{ key: string; order?: string }[]>([])

// 服务器端分页，不再需要本地排序和过滤

// 扫描
const activeAP: any = ref({})

const router = useRouter()

const remoteConfigFun = (item: any) => {
  if (item.net_type == 0) {
    // 打开新页面
    const url = `http://${item.ip}`
    const windowName = '_blank'

    window.open(url, windowName)
  }
  else {
    router.push({ name: 'device-remote', query: { id: item.id } })
  }
}

const deviceManagerFun = (item: any) => {
  $get(`/v1/device/${item.id}`, {}).then(res => {
    if (res.msg === 'success') {
      const obj = Object.assign(item, res.result)

      dealData(obj)
    }
  })
}

const workModeFormItems: any = ref([])

const nameModeFormItems: any = ref([])

// 国家码
const COUNTRY_OPTIONS_LOCALIZED = COUNTRY_OPTIONS.map(item => ({
  ...item,
  label: item.label,
}))

// 这些需要翻译的保持为计算属性
// 工作模式
const NET_TYPE_LOCALIZED = computed(() => {
  return NET_TYPE.map(item => ({
    ...item,
    label: t(item.label),
  }))
})

// 发射功率 2G
const TX_POWER_2G_LOCALIZED = computed(() => {
  return TX_POWER_2G.map(item => ({
    ...item,
    label: t(item.label),
  }))
})

// 发射功率 5G
const TX_POWER_5G_LOCALIZED = computed(() => {
  return TX_POWER_5G.map(item => ({
    ...item,
    label: t(item.label),
  }))
})

// 加密类型
const ENCRYPTION_TYPE_LOCALIZED = computed(() => {
  return ENCRYPTION_TYPE.map(item => ({
    ...item,
    label: item.label === 'None' ? t('Config.Mode.None') : item.label, // 只有None需要翻译，其他是技术标准
  }))
})

// 5G  协议
const PROTOCOL_5G_LOCALIZED = computed(() => {
  return PROTOCOL_5G.map(item => ({
    ...item,
    label: item.label, // 协议不需要翻译
  }))
})

const PROTOCOL_2G_LOCALIZED = PROTOCOL_2G.map(item => ({
  ...item,
  label: item.label, // 无需翻译，因为PROTOCOL_2G已经是技术标准名称
}))

const BAND_WIDTH_2G_LOCALIZED = BAND_WIDTH_2G.map(item => ({
  ...item,
  label: item.label, // 无需翻译，因为BAND_WIDTH_2G已经是技术参数名称
}))

// IP地址验证器 - 验证最后一位在1-254之间
const validateLanIp = (value: string) => {
  if (!value)
    return true // 允许空值，如果需要必填可以在另一个验证器中处理

  // IP地址格式验证
  const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/
  const match = value.match(ipRegex)

  if (!match)
    return t('Device.AP.InvalidIPFormat')

  // 检查每个部分是否在0-255之间
  const parts = []
  for (let i = 1; i <= 4; i++) {
    const part = Number.parseInt(match[i], 10)
    if (part < 0 || part > 255)
      return t('Device.AP.IPRangeError')
    parts.push(part)
  }

  // 验证是否为私有网络IP地址范围 (RFC 1918)
  const [p1, p2, , p4] = parts
  const isPrivateA = p1 === 10 // 10.0.0.0 - **************
  const isPrivateB = p1 === 172 && (p2 >= 16 && p2 <= 31) // ********** - **************
  const isPrivateC = p1 === 192 && p2 === 168 // *********** - ***************

  if (!(isPrivateA || isPrivateB || isPrivateC))
    return t('Device.AP.NotPrivateIP')

  // 验证最后一位不能为0或255（网络地址和广播地址）
  if (p4 === 0 || p4 === 255)
    return t('Device.AP.IPLastByteError')

  return true
}

// 其他验证器
const requiredValidator = (value: string) => !!value || t('Device.AP.Required')

// 处理数据的函数提前声明
const dealData = (info: any) => {
  const item = info.deviceInfo

  activeAP.value = info
  deviceInfoTable.value = [
    {
      label: t('Device.AP.DeviceTypeDesc'),
      value: 'AP',
    },
    {
      label: t('Device.AP.DeviceType'),
      value: info.model,
    },
    {
      label: t('Device.AP.SerialNumber'),
      value: info.sn,
    },
    {
      label: t('Device.AP.IPAddress'),
      value: item.ip || '--',
    },
    {
      label: t('Device.AP.MACAddress'),
      value: info.mac,
    },
    {
      label: t('Device.AP.FirmwareVersion'),
      value: item.version || '--',
    },
    {
      label: t('Device.AP.InterfaceRate'),
      value: item.speed ? `${item.speed} Mbps/s` : '0 Mbps/s',
    },
  ]
  if (item.wifiHtMode_2G == 'HT40') {
    if (item.wifiForce40MHzMode_2G == '0') {
      selectedBandwidth.value = '20/40M'
      HTMode.value = 'HT40'
    }
    else {
      selectedBandwidth.value = '40M'
      HTMode.value = 'HT40'
    }
  }
  else {
    selectedBandwidth.value = '20M'
    HTMode.value = 'HT20'
  }

  nameModeFormItems.value = [
    {
      label: t('Device.AP.DeviceName'),
      value: item.user_name,
      key: 'name',
      formType: 'input',
    },
  ]
  workModeFormItems.value = [
    {
      label: t('Config.Mode.NetworkType'),
      value: item.net_type,
      key: 'workMode',
      formType: 'select',
      list: NET_TYPE_LOCALIZED.value,
      subtitle: `${t('Device.AP.RouterModeAPModeDesc')}
${t('Device.AP.RouterModeDesc')}
${t('Device.AP.APModeDesc')}`,
    },
    {
      label: t('Device.AP.LANIP'),
      value: item.lan_ip,
      key: 'lan',
      formType: 'input',
      rules: [requiredValidator, validateLanIp],
    },
    {
      label: t('NetworkConfig.LAN.SubnetMask'),
      value: item.lan_netmask,
      formType: 'select',
      list: subnetMaskOptions,
      rules: [requiredValidator],
      key: 'subnetMask',
    },
    {
      label: t('Device.AP.LAN1'),
      value: item.eth1_link === 'up' ? t('Device.AP.Connected') : t('Device.AP.Disconnected'),
      key: 'lan1',
      formType: 'text',
    },
    {
      label: t('Device.AP.LAN2'),
      value: item.eth0_link === 'up' ? t('Device.AP.Connected') : t('Device.AP.Disconnected'),
      key: 'lan2',
      formType: 'text',
    },
  ]

  // 调用初始化templateForm的函数
  initTemplateForm(item, info)
}

const restartDeviceFun = (item: any) => {
  if (!item.sn) {
    ElMessage.error(t('Device.AP.NoSN'))

    return
  }

  ElMessageBox.confirm(t('Device.AP.RestartConfirm'), t('Device.AP.Tip'), {
    confirmButtonText: t('Device.AP.Confirm'),
    cancelButtonText: t('Device.AP.Cancel'),
    type: 'warning',
  }).then(() => {
    $post('', {
      requestType: 501,
      data: {
        ap_sn_list: `${item.sn} `,
      },
    }).then(res => {
      if (res.msg === 'success') {
        ElMessage.success(t('Device.AP.RestartSuccess'))
        requestId.value = res.result.bulkId
        dealRequestData()
      }
    })
  }).catch(() => {
    ElMessage.info(t('Device.AP.RestartCancelled'))
  })
}

const delDeviceFun = (item: any) => {
  if (!item.sn) {
    ElMessage.error(t('Device.AP.NoSN'))

    return
  }
  ElMessageBox.confirm(t('Device.AP.DeleteConfirm'), t('Device.AP.Tip'), {
    confirmButtonText: t('Device.AP.Confirm'),
    cancelButtonText: t('Device.AP.Cancel'),
    type: 'warning',
  }).then(() => {
    $post('', {
      requestType: 508,
      data: {
        ap_sn_list: `${item.sn} `,
      },
    }).then(res => {
      if (res.msg === 'success') {
        ElMessage.success(t('Device.AP.DeleteSuccess'))

        // 删除成功后刷新列表数据
        setTimeout(() => {
          getApList()
        }, 1000)
      }
      else {
        ElMessage.error(res.msg || t('Device.AP.DeleteFailed'))
      }
    })
  }).catch(() => {
    ElMessage.info(t('Device.AP.DeleteCancelled'))
  })
}

const getApList = () => {
  const params: any = {
    dType: 0, // AP设备类型
    page: page.value,
    size: itemsPerPage.value,
  }

  // 只有当不是“全部”且不为 null 时才加 model 参数
  if (selectedModel.value && selectedModel.value !== '')
    params.model = selectedModel.value

  $get('/v1/device/list', params).then(res => {
    if (res.msg === 'success') {
      apData.value.total = res.result.count || 0

      // 为每个AP设备添加runstatus字段，默认值为-1
      apData.value.apList = (res.result.rows || [])
        .filter((item: any) => item.sn)
        .map((item: any) => ({
          ...item,
          runStatus: -1,
        })) || []

      // 选中逻辑：
      if (selectedModel.value === '') {
        // 选择“全部”时，不选中任何表格项
        selectedRows.value = []
      }
      else if (selectedModel.value) {
        // 选择具体型号时，选中该型号的所有
        selectedRows.value = apData.value.apList.filter((item: any) => item.model === selectedModel.value).map((item: any) => item.sn)
      }
      else {
        // 初始不选中
        selectedRows.value = []
      }
    }
  })
}

// 添加分页参数监听
watch([page, itemsPerPage], () => {
  getApList()
}, { immediate: false })

onMounted(() => {
  getModelList() // 页面初始化时获取型号列表
  getApList()
  getVlanList()
})

const getAPStatus = (bulkId: string) => {
  setTimeout(() => {
    $get(`/v1/bulkCmdResult/${bulkId}`, {}).then(res => {
      if (!res.result || !Array.isArray(res.result.rows))
        return
      const statusList = res.result.rows

      for (const item of statusList) {
        const sn = item.sn

        // result: true/false, online: true/false, data: string
        let newStatus = 0
        if (item.result === true)
          newStatus = 1 // 成功

        else
          newStatus = 2 // 失败或超时

        // 根据SN更新表格数组中对应项的runstatus和online
        const apIndex = apData.value.apList.findIndex((ap: any) => ap.sn === sn)
        if (apIndex !== -1) {
          apData.value.apList[apIndex].runStatus = newStatus
          apData.value.apList[apIndex].online = item.online
          apData.value.apList[apIndex].resultData = item.data
        }
      }
    })
  }, 3000)
}

// 执行
const execute = () => {
  if (operation.value === '') {
    ElMessage.error(t('Device.AP.SelectOperation'))

    return
  }
  if (selectedRows.value.length === 0) {
    ElMessage.error(t('Device.AP.SelectAP'))

    return
  }

  // 对于需要model参数的操作（如升级），检查型号一致性
  let currentModel = selectedModel.value
  if (!currentModel && operation.value === 2) {
    // 如果没有选择型号，检查选中设备的型号是否一致
    const selectedDevices = apData.value.apList.filter((item: any) =>
      selectedRows.value.includes(item.sn),
    )

    const models = [...new Set(selectedDevices.map((item: any) => item.model))]

    if (models.length > 1) {
      ElMessage.error(t('Tip.errorStr'))

      return
    }
    else if (models.length === 1) {
      currentModel = models[0]
    }
  }
  const selectStr = `${selectedRows.value.join(' ')} ` || ''
  switch (operation.value) {
  case 0:
    // 重启
    $post('/v1/bulkCmd', {
      deviceSns: selectedRows.value,
      cmd: 'restart',
    }).then(res => {
      if (res.msg === 'success') {
        getAPStatus(res.result)
        ElMessage.success(t('Device.AP.RestartSuccess'))
      }
    })
    break
  case 1:
    // 恢复出厂设置
    $post('/v1/bulkCmd', {
      deviceSns: selectedRows.value,
      cmd: 'factoryReset',
    }).then(res => {
      if (res.msg === 'success')
        ElMessage.success(t('Device.AP.ExecuteSuccess'))
    })
    break
  case 2:
    // 升级
    const file = fileList.value[0]
    const reader = new FileReader()

    reader.onload = (event: any) => {
      const backupFileData = event.target.result.split(',')
      const fileData = backupFileData[1]

      $post('', {
        requestType: 503,
        data: {
          model: currentModel,
          ap_sn_list: selectStr,
          fileName: fileList.value[0].name,
          fileSize: fileList.value[0].size.toString(),
          reset: resetAfterUpdate.value,
          file: fileData,
        },
      }).then(data => {
          if (data.err_code == 0) {
          getAPStatus(data.result)
          ElMessage.success(t('Device.AP.UpgradeSuccess'))
        }
      })
    }
    reader.readAsDataURL(file.raw)
    break
  case 3:
    $post('', {
      requestType: 508,
      data: {
        ap_sn_list: selectStr,
      },
    }).then(res => {
      if (res.msg === 'success') {
        ElMessage.success(t('Device.AP.DeleteSuccess'))

          // 批量删除成功后刷新列表数据
          setTimeout(() => {
          getApList()
        }, 1000)

        // 不再清空选中的行
        selectedRows.value = []
      }
      else {
        ElMessage.error(res.msg || t('Device.AP.DeleteFailed'))
      }
    })
    break
  case 4:
    // 闪烁LED
    $post('/v1/bulkCmd', {
      deviceSns: selectedRows.value,
      cmd: ledMode.value === '1' ? 'ledON' : 'ledOFF',
    }).then(res => {
      if (res.msg === 'success') {
          getAPStatus(res.result)
          ElMessage.success(t('Device.AP.ExecuteSuccess'))
        }
    })
    break
  case 5:
    // 导出 SN和MAC地址
    try {
      // 根据selectedRows获取对应的AP设备信息
      const selectedDevices = apData.value.apList.filter((item: any) =>
        selectedRows.value.includes(item.sn),
      )

      if (selectedDevices.length === 0) {
        ElMessage.warning('没有选中的设备')
        return
      }

      // 组成新数组，包含MAC、SN和设备名称
      const exportData = selectedDevices.map((device: any) => ({
        mac: device.mac || '--',
        sn: device.sn || '--',
        name: device.name || '--',
      }))

      // 生成txt文件内容，不添加表头
      const content = exportData.map(item => `${item.mac}\t${item.sn}\t${item.name}`).join('\n')
      const fileContent = content

      // 创建Blob对象
      const blob = new Blob([fileContent], { type: 'text/plain;charset=utf-8' })

      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')

      link.href = url
      link.download = `AP设备信息_${new Date().toISOString().slice(0, 10)}.txt`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 释放URL对象
      URL.revokeObjectURL(url)

      ElMessage.success(`成功导出 ${selectedDevices.length} 个设备信息`)
    }
    catch (error) {
      console.error('导出设备信息失败:', error)
      ElMessage.error('导出设备信息失败')
    }
    break
  default:
    break
  }
}

const downloadFile = (url: string, fileName: string) => {
  const link = document.createElement('a')

  link.href = url
  link.download = fileName
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const openLinkInNewWindow = (url: string) => {
  if (!url)
    return

  window.open(`http://${url}`, '_blank', 'noopener,noreferrer')
}

const showCurrentPassword = ref(false)

// SSID配置类型选项列表
const ssidConfigTypeList = ref([
  {
    label: t('Config.Mode.DefaultConfig'),
    value: '0',
  },
  {
    label: t('Config.Mode.MultiSSID'),
    value: '1',
  },
])

// 多SSID配置相关状态
const ssid2gCount = ref(1) // 2.4G SSID数量，默认1个
const ssid5gCount = ref(1) // 5G SSID数量，默认1个

// 密码显示状态
const showMergePassword = ref(false)
const show2GPassword = ref(false)
const show5GPassword = ref(false)

// 模板表单数据
const templateForm = reactive({
  ssid_count: '', // SSID数量
  ssid_2g: '', //	2G SSID
  ssid_5g: '', //	5G SSID
  key_2g: '', //	2G密码
  key_5g: '', //	5G密码
  ssid_type: '0', //	1：SSID双频合一 0：SSID分开
  ssidConfigType: '0', // 0：默认配置 1：多SSID配置
  wifiOnOff_2G: '0', // 2.4GWiFi开关 0：开启 1：关闭
  wifiOnOff_5G: '0', // 5.8GWiFi开关 0：开启 1：关闭
  wifiApIsolate_2G: '0', //	2.4G AP隔离  0：关闭 1：开启
  wifiApIsolate_5G: '0', //	5.8G AP隔离  0：关闭 1：开启
  wifiEnable_2G: '0', //	2.4G SSID 隐藏  0：关闭 1：开启
  wifiEnable_5G: '0', //	5.8G SSID 隐藏  0：关闭 1：开启
  wifiCountry_2G: 'CN', //	2.4G 国家代码例如中国传值CN
  wifiCountry_5G: 'CN', //	5.8G 国家代码
  wifiChannel_2G: 'auto', //	2.4G 信道
  wifiChannel_5G: 'auto', //	5.8G 信道
  wifiHwMode_2G: '11axg', //	2.4G协议，默认802.11ax
  wifiHwMode_5G: '11axa', //	5.8G协议，默认802.11ax
  wifiHtMode_2G: 'HT20', //	2.4G带宽
  wifiForce40MHzMode_2G: '0', //	2.4G强制带宽40M选项
  wifiHtMode_5G: 'HT160', //	5.8G带宽
  wifiTxpower_2G: '', // 2.4G信号调节
  wifiTxpower_5G: '', // 5.8G信号调节
  wifi80211r_2G: '', //	2.4G快速漫游r协议
  wifi80211r_5G: '', //	5.8G快速漫游r协议
  wifiAuthmode_2G: 'mixed-psk', //	2.4G加密方式选项，默认wpa1/wpa2
  wifiAuthmode_5G: 'mixed-psk', //	5.8G加密方式选项，默认wpa1/wpa2
  wifiWpa3_2G: '', //	2.4G wpa3 开启标志
  wifiWpa3_5G: '', //	5.8G wpa3 开启标志
  // 多SSID配置字段
  ssid_2g2: '', // 2.4G SSID 2
  key_2g2: '', // 2.4G 密码 2
  vlanId24G1: '', // 2.4G SSID 1 VLAN ID
  vlanId24G2: '', // 2.4G SSID 2 VLAN ID
  vlanTemplateId24G1: '', // 2.4G SSID 1 VLAN模板ID
  vlanTemplateId24G2: '', // 2.4G SSID 2 VLAN模板ID
  wifiMaxsta_2G2: '', // 2.4G SSID 2 最大连接数
  wifiApIsolate_2G2: '0', // 2.4G SSID 2 AP隔离
  ssid_5g2: '', // 5G SSID 2
  key_5g2: '', // 5G 密码 2
  vlanId5G1: '', // 5G SSID 1 VLAN ID
  vlanId5G2: '', // 5G SSID 2 VLAN ID
  vlanTemplateId5G1: '', // 5G SSID 1 VLAN模板ID
  vlanTemplateId5G2: '', // 5G SSID 2 VLAN模板ID
  wifiMaxsta_5G2: '', // 5G SSID 2 最大连接数
  wifiApIsolate_5G2: '0', // 5G SSID 2 AP隔离
  // 双频合一的参数
  ssid: '', // SSID名称
  encryptionType: 'mixed-psk', // 加密类型，默认wpa1/wpa2
  password: '', // 密码
  isolate: false, // 隔离
  wifiMaxsta_2G: '',
  wifiMaxsta_5G: '',
})

const workForm = reactive({
  net_type: '1', //	0：路由模式 1：AP模式，默认AP模式
  vlanId: '', // VLAN ID (1-4094)
  vlanTemplateId: '', // VLAN模板ID
  ap_lan_ip: '', //	LAN IP
  ap_lan_mask: '', //	LAN子网掩码
  networkLimit: '0', //	网络限速 0：关闭 1：开启
  upstreamLimit: '', //	上行
  upstreamLimit: '', //	下行
})

// 增加SSID
const addSSID2G = () => {
  if (ssid2gCount.value < 2)
    ssid2gCount.value++
}

const addSSID5G = () => {
  if (ssid5gCount.value < 2)
    ssid5gCount.value++
}

// 删除SSID
const removeSSID2G = () => {
  if (ssid2gCount.value > 1) {
    ssid2gCount.value--

    // 清空第二个SSID的数据
    templateForm.ssid_2g2 = ''
    templateForm.key_2g2 = ''
    templateForm.vlanId24G2 = ''
    templateForm.wifiMaxsta_2G2 = ''
    templateForm.wifiApIsolate_2G2 = '0'
  }
}

const removeSSID5G = () => {
  if (ssid5gCount.value > 1) {
    ssid5gCount.value--

    // 清空第二个SSID的数据
    templateForm.ssid_5g2 = ''
    templateForm.key_5g2 = ''
    templateForm.vlanId5G2 = ''
    templateForm.wifiMaxsta_5G2 = ''
    templateForm.wifiApIsolate_5G2 = '0'
  }
}

// SSID配置类型切换处理
const ssidConfigTypeChange = () => {
  // 切换到多SSID配置时，重置双频合一为分开模式
  if (templateForm.ssidConfigType === '1')
    templateForm.ssid_type = '0'
}

// SSID类型切换处理
const ssidTypeChange = () => {
  // 可以在这里添加切换逻辑
}

// 初始化templateForm数据的函数 - 相当于editTemplate，带着现有值
const initTemplateForm = (item: any, info: any) => {
  // 基础SSID配置类型判断
  // 如果有第二个SSID，则为多SSID配置，否则为默认配置
  const hasSecondSSID = item.ssid_2g2 || item.ssid_5g2

  templateForm.ssidConfigType = hasSecondSSID ? '1' : '0'

  templateForm.ssid_type = item.ssid_type || '0'

  // 双频合一配置
  if (item.ssid_type === '1') {
    templateForm.ssid = item.ssid_2g || ''
    templateForm.encryptionType = item.wifiAuthmode_2G || 'mixed-psk'
    templateForm.password = item.key_2g || ''
    templateForm.isolate = item.wifiApIsolate_2G == '1'
  }

  // 2.4G配置
  templateForm.ssid_2g = item.ssid_2g || ''
  templateForm.key_2g = item.key_2g || ''
  templateForm.wifiAuthmode_2G = item.wifiAuthmode_2G || 'mixed-psk'
  templateForm.wifiOnOff_2G = item.wifiOnOff_2G || '0'
  templateForm.wifiApIsolate_2G = item.wifiApIsolate_2G || '0'
  templateForm.wifiHwMode_2G = item.wifiHwMode_2G || '11axg'
  templateForm.wifiCountry_2G = item.wifiCountry_2G || 'CN'
  templateForm.wifiChannel_2G = item.wifiChannel_2G || 'auto'
  templateForm.wifiTxpower_2G = item.wifiTxpower_2G || ''
  templateForm.wifiMaxsta_2G = item.wifiMaxsta_2G || ''

  // 5G配置
  templateForm.ssid_5g = item.ssid_5g || ''
  templateForm.key_5g = item.key_5g || ''
  templateForm.wifiAuthmode_5G = item.wifiAuthmode_5G || 'mixed-psk'
  templateForm.wifiOnOff_5G = item.wifiOnOff_5G || '0'
  templateForm.wifiApIsolate_5G = item.wifiApIsolate_5G || '0'
  templateForm.wifiHwMode_5G = item.wifiHwMode_5G || '11axa'
  templateForm.wifiCountry_5G = item.wifiCountry_5G || 'CN'
  templateForm.wifiChannel_5G = item.wifiChannel_5G || 'auto'
  templateForm.wifiHtMode_5G = item.wifiHtMode_5G || 'HT80'
  templateForm.wifiTxpower_5G = item.wifiTxpower_5G || ''
  templateForm.wifiMaxsta_5G = item.wifiMaxsta_5G || ''

  // 多SSID配置（第二个SSID）
  if (hasSecondSSID) {
    // 2.4G SSID 2
    templateForm.ssid_2g2 = item.ssid_2g2 || ''
    templateForm.key_2g2 = item.key_2g2 || ''
    templateForm.wifiAuthmode_2G2 = item.wifiAuthmode_2G2 || 'mixed-psk'
    templateForm.wifiMaxsta_2G2 = item.wifiMaxsta_2G2 || ''
    templateForm.wifiApIsolate_2G2 = item.wifiApIsolate_2G2 || '0'
    templateForm.vlanId24G2 = item.vlanId24G2 || ''

    // 5G SSID 2
    templateForm.ssid_5g2 = item.ssid_5g2 || ''
    templateForm.key_5g2 = item.key_5g2 || ''
    templateForm.wifiAuthmode_5G2 = item.wifiAuthmode_5G2 || 'mixed-psk'
    templateForm.wifiMaxsta_5G2 = item.wifiMaxsta_5G2 || ''
    templateForm.wifiApIsolate_5G2 = item.wifiApIsolate_5G2 || '0'
    templateForm.vlanId5G2 = item.vlanId5G2 || ''

    // 更新SSID计数
    ssid2gCount.value = item.ssid_2g2 ? 2 : 1
    ssid5gCount.value = item.ssid_5g2 ? 2 : 1
  }

  //  vlan 信息赋值 无线状态
  templateForm.vlanTemplateId24G1 = item.vlanTemplateId24G1 || ''
  templateForm.vlanTemplateId24G2 = item.vlanTemplateId24G2 || ''
  templateForm.vlanTemplateId5G1 = item.vlanTemplateId5G1 || ''
  templateForm.vlanTemplateId5G2 = item.vlanTemplateId5G2 || ''
  templateForm.vlanId24G1 = item.vlanId24G1 || ''
  templateForm.vlanId24G2 = item.vlanId24G2 || ''
  templateForm.vlanId5G1 = item.vlanId5G1 || ''
  templateForm.vlanId5G2 = item.vlanId5G2 || ''
  console.log(templateForm)

  workForm.ap_lan_ip = item.lan_ip || ''
  workForm.ap_lan_mask = item.lan_netmask || ''
  workForm.net_type = item.net_type || '1'
  workForm.vlanId = item.vlanId || ''
  workForm.vlanTemplateId = item.vlanTemplateId || ''

  // workForm.networkLimit = item.networkLimit || '0'
  // workForm.upstreamLimit = item.upstreamLimit || ''
  // workForm.downstreamLimit = item.downstreamLimit || ''
  drawer.value = true
}

// 从config/mode移动过来的templateForm SSID相关监听器

const channelOptions2g = computed(() => {
  const index = COUNTRY_OPTIONS.findIndex((data: any) => data.value === templateForm.wifiCountry_2G)

  return CHANNEL_ARR_2G[index] || []
})

const channelOptions5g = computed(() => {
  const index = COUNTRY_OPTIONS.findIndex((data: any) => data.value === templateForm.wifiCountry_5G)

  return CHANNEL_ARR_5G[index] || []
})

// 监听国家码变化，自动重置信道为auto
watch(() => templateForm.wifiCountry_2G, () => {
  templateForm.wifiChannel_2G = 'auto'
}, { immediate: true })

watch(() => templateForm.wifiCountry_5G, () => {
  templateForm.wifiChannel_5G = 'auto'
}, { immediate: true })

const roamingParameterFormItems = ref([{
  label: '快速漫游协议（802.11k/v/r）',
  value: '',
  key: 'roaming',
  formType: 'switch',
}, {
  label: '弱信号阈值',
  value: '',
  key: 'weakSignal',
  formType: 'input',
  subtitle: '推荐范围：-80 ~ -70 dBm',
}, {
  label: '负载均衡间隔',
  value: '',
  key: 'ssidInterval',
  formType: 'select',
}, {
  label: '忽略弱信号',
  value: '',
  key: 'ignoreWeakSignal',
  formType: 'input',
  subtitle: '推荐范围：-90 ~ -80 dBm',
}, {
  label: '忽略过度重传',
  value: '',
  key: 'ignoreOverload',
  formType: 'select',
}])

// 添加缺失的变量定义
const vlanList = ref([])

const getVlanList = () => {
  $get('/v1/vlanConfigs', { page: 1, size: 1000 }).then((res: any) => {
    if (res.msg === 'success' || res.msg === 'success') {
      // 兼容不同后端返回结构
      const rows = res.result?.rows || []

      vlanList.value = [
        { id: '', vlanId: '' },
        ...rows,
      ]

      vlanList.value.forEach(item => {
        if (item.id)
          item.itemTitle = `${item.name} VLAN ${item.vlanId} ${item.vlanId}`

        else
          item.itemTitle = t('PleaseSelect')
      })
    }
    else {
      ElMessage.error(res.err_message || res.msg || '获取VLAN列表失败')
    }
  })
}

// 密码验证函数
const validatePasswordEight = (value: string) => {
  // 默认配置且双频合一时不需要验证分开的密码
  if (templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1' || templateForm.ssidConfigType === '1')
    return true

  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_2G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

// 密码验证函数
const validatePasswordEight5GMore = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_5G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

// 验证函数
const requiredValidatorNew = (value: string, message: string) => {
  return value ? true : message
}

const validatePassword = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.encryptionType === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEightMore = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_2G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight5G = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_5G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

// VLAN选择处理
const handleVlanSelect = (field: string, value: any) => {
  // 处理VLAN选择逻辑
  console.log('VLAN selected:', field, value)
}

const saveConfig = () => {
  console.log('saveConfig', activeAP.value)
  if (currentTab.value === 0) {
    $post('', {
      requestType: 513,
      data: {
        name: nameModeFormItems.value[0].value,
        ap_sn_list: `${activeAP.value.sn} `,
      },
    }).then(res => {
      if (res.msg === 'success') {
        ElMessage.success(t('Config.Mode.SaveSuccess'))
        drawer.value = false
        getApList()
      }
      else {
        ElMessage.error(res.msg || t('Device.AP.ModeSwitchFailed'))
      }
    })
  }

  if (currentTab.value === 1) {
    // 使用templateForm数据进行保存
    console.log(templateForm)

    let {
      ssid_count,
      ssid_2g,
      ssid_5g,
      key_2g,
      key_5g,
      ssid_type,
      ssidConfigType,
      wifiOnOff_2G,
      wifiOnOff_5G,
      wifiApIsolate_2G,
      wifiApIsolate_5G,
      wifiEnable_2G,
      wifiEnable_5G,
      wifiCountry_2G,
      wifiCountry_5G,
      wifiChannel_2G,
      wifiChannel_5G,
      wifiHwMode_2G,
      wifiHwMode_5G,
      wifiHtMode_2G,
      wifiForce40MHzMode_2G,
      wifiHtMode_5G,
      wifiTxpower_2G,
      wifiTxpower_5G,
      wifiAuthmode_2G,
      wifiAuthmode_5G,
      wifiWpa3_2G,
      wifiWpa3_5G,
      ssid,
      encryptionType,
      password,
      isolate,
      wifiMaxsta_2G,
      wifiMaxsta_5G,

      // 多SSID配置字段
      ssid_2g2,
      key_2g2,
      wifiMaxsta_2G2,
      wifiApIsolate_2G2,
      ssid_5g2,
      key_5g2,
      wifiMaxsta_5G2,
      wifiApIsolate_5G2,
      vlanId24G1,
      vlanId24G2,
      vlanId5G1,
      vlanId5G2,
      vlanTemplateId24G1,
      vlanTemplateId24G2,
      vlanTemplateId5G1,
      vlanTemplateId5G2,
    } = templateForm

    const vlan24G1 = vlanList.value.find(v => v.id === vlanTemplateId24G1)
    const vlan24G2 = vlanList.value.find(v => v.id === vlanTemplateId24G2)
    const vlan5G1 = vlanList.value.find(v => v.id === vlanTemplateId5G1)
    const vlan5G2 = vlanList.value.find(v => v.id === vlanTemplateId5G2)

    vlanId24G1 = vlan24G1?.vlanId || ''
    vlanId24G2 = vlan24G2?.vlanId || ''
    vlanId5G1 = vlan5G1?.vlanId || ''
    vlanId5G2 = vlan5G2?.vlanId || ''

    // 处理默认配置的双频合一
    if (ssidConfigType === '0' && ssid_type == '1') {
      ssid_2g = ssid
      ssid_5g = ssid
      key_2g = password
      key_5g = password
      wifiApIsolate_2G = isolate ? '1' : '0'
      wifiApIsolate_5G = isolate ? '1' : '0'
      wifiAuthmode_2G = encryptionType
      wifiAuthmode_5G = encryptionType
      tpl_bands = '2.4G/5G'
    }

    // 处理多SSID配置的SSID数量
    if (ssidConfigType === '1') {
      let count = 0
      if (wifiOnOff_2G == '0')
        count += ssid2gCount.value
      if (wifiOnOff_5G == '0')
        count += ssid5gCount.value
      ssid_count = count.toString()
    }
    else {
      ssid_count = '1'
    }

    if (wifiAuthmode_2G === 'psk2')
      wifiWpa3_2G = '1'
    else
      wifiWpa3_2G = '0'

    if (wifiAuthmode_5G === 'psk2')
      wifiWpa3_5G = '1'
    else
      wifiWpa3_5G = '0'

    if (wifiAuthmode_2G == 'none')
      key_2g = ''

    if (wifiAuthmode_5G == 'none')
      key_5g = ''

    const postData = {
      ssidConfigType,
      ssid_count,
      ssid_2g,
      ssid_5g,
      key_2g,
      key_5g,
      ssid_type,
      wifiOnOff_2G,
      wifiOnOff_5G,
      wifiApIsolate_2G,
      wifiApIsolate_5G,
      wifiEnable_2G,
      wifiEnable_5G,
      wifiCountry_2G,
      wifiCountry_5G,
      wifiChannel_2G,
      wifiChannel_5G,
      wifiHwMode_2G,
      wifiHwMode_5G,
      wifiHtMode_2G,
      wifiForce40MHzMode_2G,
      wifiHtMode_5G,
      wifiTxpower_2G,
      wifiTxpower_5G,
      wifiAuthmode_2G,
      wifiAuthmode_5G,
      wifiWpa3_2G,
      wifiWpa3_5G,
      wifiMaxsta_2G,
      wifiMaxsta_5G,
      vlanId24G1,
      vlanId24G2,
      vlanId5G1,
      vlanId5G2,
      ssid_2g2,
      key_2g2,
      wifiMaxsta_2G2,
      wifiApIsolate_2G2,
      ssid_5g2,
      key_5g2,
      wifiMaxsta_5G2,
      wifiApIsolate_5G2,
      vlanTemplateId24G1,
      vlanTemplateId24G2,
      vlanTemplateId5G1,
      vlanTemplateId5G2,
    }

    console.log(postData)

    // todo   需要重新处理
  }
  if (currentTab.value === 2) {
    // 验证LAN IP格式
    const lanIpValue = workForm.ap_lan_ip
    const ipValidationResult = validateLanIp(lanIpValue)

    if (ipValidationResult !== true) {
      ElMessage.error(ipValidationResult)

      return
    }
    const { vlanTemplateId, net_type, vlanId, ap_lan_ip, ap_lan_mask } = workForm
    const vlan = vlanList.value.find(v => v.id === vlanTemplateId)

    vlanId = vlan?.vlanId || ''

    console.log({
      vlanId,
      net_type,
      ap_lan_ip,
      ap_lan_mask,
      vlanTemplateId,
    })
  }
  if (currentTab.value === 3) { /* empty */ }
}

const dealRequestData = () => {
  $gett(`/v1/bulkCmdResult/${requestId.value}`, { }).then(res => {
    if (res.msg === 'success') {
      //  获取设置的数组
      const statusList = res.info.apstatus.filter((item: any) => item.status && item.status !== '0')

      //  当前位置只有一个可以直接处理第一个就可以
      const status = Number(statusList[0].status)
      if (status) {
        // 请求成功，关闭抽屉
        drawer.value = false
        ElMessage.success(t('Device.AP.ConfigSuccess'))
      }
      else {
        // 失败超时
        ElMessage.error(t('Device.AP.VerificationTimeout'))
        drawer.value = false
      }
    }
    else {
      ElMessage.error(res.msg || t('Device.AP.GetStatusFailed'))
      drawer.value = false
    }
    getApList()
  })
}

const switchNetwork = (rate: number) => {
  if (!rate)
    return '0 B/s'

  // 确保 rate 是数字类型
  const rateNum = Number(rate)
  if (isNaN(rateNum))
    return '0 B/s'

  if (rateNum < 1024)
    return `${rateNum.toFixed(2)} B/s`

  else if (rateNum < 1024 * 1024)
    return `${(rateNum / 1024).toFixed(2)} KB/s`

  else if (rateNum < 1024 * 1024 * 1024)
    return `${(rateNum / (1024 * 1024)).toFixed(2)} MB/s`

  else
    return `${(rateNum / (1024 * 1024 * 1024)).toFixed(2)} GB/s`
}

// 优化：根据templateForm的5G信道动态计算可用的带宽选项
const BAND_WIDTH_5G_LOCALIZED = computed(() => {
  const channel = templateForm.wifiChannel_5G

  // 默认情况或自动信道：提供所有带宽选项
  if (!channel || channel === 'auto') {
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }

  const channelNum = Number.parseInt(channel, 10)

  // 根据信道范围返回相应的带宽选项
  if (channelNum >= 36 && channelNum <= 128) {
    // 36-128 可选20, 40, 80, 160
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }
  else if (channelNum === 132 || channelNum === 136) {
    // 132、136 可选20, 40
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
    ]
  }
  else if (channelNum === 140 || channelNum === 144) {
    // 140、144只能选20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
  else if (channelNum >= 149 && channelNum <= 161) {
    // 149-161 可选 20, 40, 80
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
    ]
  }
  else {
    // 161以上可选 20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
})

const changeChannel = () => {
  const newBandWidthOptions = BAND_WIDTH_5G_LOCALIZED.value
  const currentValue = templateForm.wifiHtMode_5G
  const isValidOption = newBandWidthOptions.some(option => option.value === currentValue)

  if (!isValidOption && newBandWidthOptions.length > 0) {
    // 如果当前选择不可用，则默认选择列表中第一个选项
    templateForm.wifiHtMode_5G = newBandWidthOptions[0].value
  }
}

// 排序事件
const sortchange = (val: any) => {
  sortBy.value = val
}

// 监听selectedModel变化，切换型号时重新获取数据并处理选中逻辑
watch(selectedModel, () => {
  page.value = 1 // 重置到第一页
  getApList()
})
</script>

<template>
  <div class="ap-manager">
    <VCard class="mb-6">
      <div class="d-flex flex-wrap gap-4 ma-6">
        <div class="d-flex align-center">
          <div class="cardTitle">
            {{ t('Device.AP.Title') }}
          </div>
        </div>
        <VSpacer />
      </div>
      <VDivider class="mt-4" />
      <div class="pa-6 d-flex">
        <AppSelect
          v-model="selectedModel"
          :items="modelList"
          class="mr-4"
          item-title="label"
          item-value="value"
          :placeholder="t('Config.AP.PleaseSelectModel')"
        />
        <AppSelect
          v-model="operation"
          :items="apHandlers"
          class="mr-4"
          item-title="label"
          item-value="value"
          :placeholder="t('Device.AP.SelectOperation')"
          @update:model-value="operationChange"
        />
        <!-- 升级 -->
        <div
          v-if="operation === 2"
          class="d-flex align-center mr-4"
        >
          <ElUpload
            ref="uploader"
            :auto-upload="false"
            :limit="1"
            :multiple="false"
            :on-change="handleChange"
            :on-exceed="handleExceed"
            :show-file-list="false"
            class="mr-4"
          >
            <span class="mr-4 text-secondary">{{
              fileList && fileList.length ? fileList[0].name : t('Device.AP.NoFileSelected')
            }}</span>
            <VBtn
              color="primary"
              variant="tonal"
            >
              {{ t('Device.AP.SelectFile') }}
            </VBtn>
          </ElUpload>
          <VCheckbox
            v-model="resetAfterUpdate"
            false-value="0"
            :label="t('Device.AP.RestoreFactory')"
            true-value="1"
          />
        </div>
        <!-- led -->
        <div
          v-if="operation === 4"
          class="mr-4"
        >
          <VCheckbox
            v-model="ledMode"
            :label="ledMode === '1' ? t('Device.AP.Blink') : t('Device.AP.Normal')"
            false-value="0"
            true-value="1"
          />
        </div>
        <VBtn @click="execute">
          <VIcon icon="tabler-checks" />
          {{ t('Device.AP.Confirm') }}
        </VBtn>
      </div>
      <VDivider />
      <VDataTable
        v-model="selectedRows"
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="apData.apList"
        :items-length="apData.total"
        class="text-no-wrap"
        item-value="sn"
        show-select
        select-strategy="page"
        :no-data-text="t('NoData')"
        :sort-by="sortBy"
        @update:sort-by="sortchange"
      >
        <template #item.name="{ item }">
          <div>{{ item.name || '--' }}</div>
        </template>
        <template #item.ip="{ item }">
          <div
            class="text-primary cursor-pointer hover-underline"
            @click="openLinkInNewWindow(item.deviceInfo.wan_ip)"
          >
            {{
              item.deviceInfo.wan_ip
                || '--'
            }}
          </div>
        </template>
        <template #item.mac="{ item }">
          {{ item.mac || '--' }}
        </template>
        <template #item.onoff="{ item }">
          <template v-if="item.runStatus == -1">
            <VChip
              v-if="item.online"
              label
              color="success"
              size="small"
            >
              {{ t('Device.AP.Online') }}
            </VChip>
            <VChip
              v-else
              label
              color="error"
              size="small"
            >
              {{ t('Device.AP.Offline') }}
            </VChip>
          </template>
          <template v-else>
            <span v-if="item.runStatus == 1">
              <img
                width="30"
                height="30"
                src="@images/success.png"
                alt=""
              >
            </span>
            <span v-if="item.runStatus == 2">{{ t('TimeOut') }}</span>
          </template>
        </template>
        <template #item.uptime="{ item }">
          {{ item.runtime || '--' }}
        </template>
        <template #item.actions="{ item }">
          <IconBtn>
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem @click="remoteConfigFun(item)">
                  {{ t('Device.AP.RemoteConfig') }}
                </VListItem>

                <VListItem @click="deviceManagerFun(item)">
                  {{ t('Device.AP.DeviceManagement') }}
                </VListItem>

                <VListItem @click="restartDeviceFun(item)">
                  {{ t('Device.AP.RestartDevice') }}
                </VListItem>

                <VListItem @click="delDeviceFun(item)">
                  {{ t('Device.AP.DeleteDevice') }}
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="apData.total"
          />
        </template>
      </VDataTable>
    </VCard>

    <VNavigationDrawer
      v-if="drawer"
      v-model="drawer"
      location="right"
      temporary
      persistent
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ t('Device.AP.DeviceManagement') }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="drawer = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-hidden">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="pa-4 d-flex flex-column h-100">
              <div class="flex-shrink-0">
                <div class="d-flex mb-6">
                  <div
                    class="rounded mr-4"
                    style="block-size: 66px;inline-size: 66px;"
                  >
                    <svg
                      width="66"
                      height="66"
                      viewBox="0 0 66 66"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M54.6616 1H11.3384C5.62867 1 1 5.62867 1 11.3384V54.6616C1 60.3713 5.62867 65 11.3384 65H54.6616C60.3713 65 65 60.3713 65 54.6616V11.3384C65 5.62867 60.3713 1 54.6616 1Z"
                        fill="url(#paint0_linear_1247_23640)"
                      />
                      <path
                        d="M54.6616 1H11.3384C5.62867 1 1 5.62867 1 11.3384V54.6616C1 60.3713 5.62867 65 11.3384 65H54.6616C60.3713 65 65 60.3713 65 54.6616V11.3384C65 5.62867 60.3713 1 54.6616 1Z"
                        stroke="url(#paint1_linear_1247_23640)"
                        stroke-width="1.64103"
                      />
                      <path
                        d="M17.6836 25.3459V40.6622C17.6836 44.8916 21.1123 48.3203 25.3417 48.3203H40.6581C44.8875 48.3203 48.3161 44.8916 48.3161 40.6622V25.3459C48.3161 21.1164 44.8875 17.6877 40.6581 17.6877H25.3417C21.1123 17.6877 17.6836 21.1164 17.6836 25.3459Z"
                        fill="#3773F5"
                        stroke="#3773F5"
                        stroke-width="2.05128"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M36.8267 25.3457H29.168"
                        stroke="white"
                        stroke-width="2.05128"
                        stroke-linejoin="round"
                      />
                      <defs>
                        <linearGradient
                          id="paint0_linear_1247_23640"
                          x1="33"
                          y1="0.179487"
                          x2="33"
                          y2="65.8205"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop stop-color="#F0F5FF" />
                          <stop
                            offset="1"
                            stop-color="#EFF4FF"
                          />
                        </linearGradient>
                        <linearGradient
                          id="paint1_linear_1247_23640"
                          x1="33"
                          y1="0.179487"
                          x2="33"
                          y2="64.1795"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop
                            stop-color="#E1EBFF"
                            stop-opacity="0"
                          />
                          <stop
                            offset="1"
                            stop-color="#DEE9FF"
                          />
                        </linearGradient>
                      </defs>
                    </svg>
                  </div>
                  <div class="d-flex flex-column justify-space-around">
                    <div class="d-flex align-center">
                      <span class="text-h5">{{ activeAP.name || '--' }}</span>
                      <VChip
                        v-if="activeAP.onoff === 'online'"
                        class="ml-2"
                        color="success"
                        size="small"
                      >
                        {{ t('Device.AP.Online') }}
                      </VChip>
                      <VChip
                        v-else
                        class="ml-2"
                        color="error"
                        size="small"
                      >
                        {{ t('Device.AP.Offline') }}
                      </VChip>
                    </div>
                    <div class="d-flex align-center text-subtitle-1">
                      <div class="text-primary mr-2">
                        <VIcon
                          icon="tabler-arrow-narrow-down"
                          size="x-small"
                        />
                        {{ switchNetwork(activeAP.deviceInfo.tx_rate) }}
                      </div>
                      <div class="text-success">
                        <VIcon
                          icon="tabler-arrow-narrow-up"
                          size="x-small"
                        />
                        {{ switchNetwork(activeAP.deviceInfo.rx_rate) }}
                      </div>
                    </div>
                  </div>
                </div>

                <VTabs v-model="currentTab">
                  <VTab :value="0">
                    {{ t('Device.AP.DeviceInfo') }}
                  </VTab>
                  <VTab :value="1">
                    {{ t('Device.AP.WirelessStatus') }}
                  </VTab>
                  <VTab :value="2">
                    {{ t('Device.AP.WorkMode') }}
                  </VTab>
                </VTabs>
              </div>

              <div class="flex-grow-1 overflow-hidden mt-4">
                <VWindow
                  v-model="currentTab"
                  class="h-100 d-flex flex-column"
                >
                  <VWindowItem
                    :value="0"
                    class="flex-grow-1 overflow-auto"
                    style=" -ms-overflow-style: none;scrollbar-width: none;"
                  >
                    <div class="bg-grey-light pa-4 my-4 rounded">
                      <VRow>
                        <VCol>
                          <div class="label">
                            {{ t('Device.AP.InternetStatus') }}
                          </div>
                          <div
                            v-if="activeAP.deviceInfo.internet === '0'"
                            class="value text-success"
                          >
                            {{ t('Device.AP.Online') }}
                          </div>
                          <div
                            v-else
                            class="value text-error"
                          >
                            {{ t('Device.AP.Offline') }}
                          </div>
                        </VCol>
                        <VCol>
                          <div class="label">
                            {{ t('Device.AP.RunningTime') }}
                          </div>
                          <div class="value">
                            {{ activeAP.deviceInfo.runtime }}
                          </div>
                        </VCol>
                        <VCol>
                          <div class="label">
                            {{ t('Config.Mode.NetworkType') }}
                          </div>
                          <div class="value">
                            <div v-if="activeAP.deviceInfo.vlanId">
                              {{ t('NetworkConfig.Modes.VLANMode') }}
                            </div>
                            <div v-else>
                              {{ activeAP.deviceInfo.net_type === '0' ? t('Device.AP.RouterMode') : t('Device.AP.APMode') }}
                            </div>
                          </div>
                        </VCol>
                      </VRow>
                    </div>
                    <VForm>
                      <div
                        v-for="(item, index) in nameModeFormItems"
                        :key="index"
                        class="d-flex align-start mb-4"
                      >
                        <div class="w-80 h-38 flex-0-0 mr-4 text-subtitle-2">
                          {{ item.label }}
                        </div>
                        <div class="w-100">
                          <AppTextField
                            v-if="item.formType === 'input'"
                            v-model="item.value"
                            :rules="item.rules"
                            append-inner-icon="tabler-edit"
                          />
                          <div
                            v-if="item.subtitle"
                            class="text-subtitle-2"
                          >
                            {{ item.subtitle }}
                          </div>
                        </div>
                      </div>
                    </VForm>
                    <div>
                      <div
                        v-for="(item, index) in deviceInfoTable"
                        :key="index"
                        class="d-flex h-38 mb-4"
                      >
                        <div class="flex-0-0 mr-4 w-80 text-on-surface opacity-90 text-subtitle-2">
                          {{ item.label }}
                        </div>
                        <div class="flex-1-0 text-secondary opacity-70 text-subtitle-1">
                          {{ item.value }}
                        </div>
                      </div>
                    </div>
                  </VWindowItem>
                  <VWindowItem
                    :value="1"
                    class="flex-grow-1 overflow-auto"
                    style=" -ms-overflow-style: none;scrollbar-width: none;"
                  >
                    <!-- 性能优化：延迟加载标签页内容 -->
                    <VForm
                      v-if="tabContentLoaded[1]"
                      ref="formRef2"
                    >
                      <!-- SSID配置类型选择按钮组 -->
                      <div class="d-flex justify-space-between align-center mb-4">
                        <BtnGroupSelector
                          v-model:value="templateForm.ssidConfigType"
                          fill-row
                          :options="ssidConfigTypeList"
                          @update:value="ssidConfigTypeChange"
                        />
                      </div>

                      <!-- 双频合一开关 (仅默认配置显示) -->
                      <div
                        v-if="templateForm.ssidConfigType === '0'"
                        class="d-flex justify-space-between align-center mb-4"
                      >
                        <div class="text-subtitle-2 text-on-surface opacity-90">
                          {{ t('Config.AP.DualBandUnify') }}
                        </div>
                        <div class="d-flex align-center">
                          <VSwitch
                            v-model="templateForm.ssid_type"
                            class="mr-2"
                            false-value="0"
                            true-value="1"
                            @update:model-value="ssidTypeChange"
                          />
                          <span class="text-subtitle-2 text-on-surface opacity-50">
                            {{ t('Config.AP.DualBandUnifyHint') }}
                          </span>
                        </div>
                      </div>
                      <!-- 默认配置 - 双频合一 -->
                      <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1'">
                        <!-- 双频合一SSID名称输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <!-- 双频合一加密类型选择 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EncryptionType') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.encryptionType"
                            :items="ENCRYPTION_TYPE_LOCALIZED"
                            :placeholder="t('Config.Mode.SelectEncryption')"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.SelectEncryption'))]"
                            item-title="label"
                            item-value="value"
                          />
                        </div>
                        <!-- 双频合一密码输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.password"
                            :append-inner-icon="showMergePassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePassword]"
                            :type="showMergePassword ? 'text' : 'password'"
                            @click:append-inner="
                              showMergePassword = !showMergePassword
                            "
                          />
                        </div>
                        <!-- 双频合一客户端隔离开关 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Isolate') }}
                          </div>
                          <div class="d-flex align-center">
                            <VSwitch
                              v-model="templateForm.isolate"
                              class="mr-2"
                            />
                            <span class="text-subtitle-2 text-on-surface opacity-50">
                              {{ templateForm.isolate ? t('Config.Mode.On') : t('Config.Mode.Off') }}
                            </span>
                          </div>
                        </div>
                      </div>
                      <VDivider class="mb-4" />
                      <div class="d-flex justify-space-between align-center mb-4">
                        <div class="text-primary text-h5">
                          {{ t('Config.Mode.WirelessSettings2G') }}
                        </div>
                        <VBtn
                          v-if="templateForm.ssidConfigType === '1' && ssid2gCount < 2"
                          color="primary"
                          size="small"
                          variant="outlined"
                          @click="addSSID2G"
                        >
                          {{ t('Config.Mode.AddSSID') }}
                        </VBtn>
                      </div>

                      <!-- 默认配置 - 2.4G分开模式 -->
                      <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'">
                        <!-- 启用Wi-Fi开关 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EnableWiFi') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiOnOff_2G"
                            false-value="1"
                            true-value="0"
                            :label="templateForm.wifiOnOff_2G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                        <!-- SSID名称输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_2g"
                            :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <!-- 2.4G加密类型选择 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EncryptionType') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.wifiAuthmode_2G"
                            :items="ENCRYPTION_TYPE_LOCALIZED"
                            item-title="label"
                            item-value="value"
                            :placeholder="t('Config.Mode.SelectEncryption')"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.SelectEncryption'))]"
                          />
                        </div>
                        <!-- 2.4G密码输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_2g"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEight]"
                            :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show2GPassword ? 'text' : 'password'"
                            @click:append-inner="show2GPassword = !show2GPassword"
                          />
                        </div>
                      </div>

                      <!-- 多SSID配置 - 2.4G WiFi开关 -->
                      <div v-if="templateForm.ssidConfigType === '1'">
                        <!-- 2.4G WiFi总开关 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EnableWiFi') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiOnOff_2G"
                            false-value="1"
                            true-value="0"
                            :label="templateForm.wifiOnOff_2G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </div>

                      <!-- 通用2.4G设置 -->
                      <!-- 2.4G协议选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Protocol') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiHwMode_2G"
                          :items="PROTOCOL_2G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectProtocol')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectProtocol')"
                        />
                      </div>
                      <!-- 2.4G国家码选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Country') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiCountry_2G"
                          :items="COUNTRY_OPTIONS_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectCountry')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectCountry')"
                          @update:model-value="() => {
                            templateForm.wifiChannel_2G = 'auto'
                          }"
                        />
                      </div>
                      <!-- 2.4G信道选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Channel') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiChannel_2G"
                          :items="channelOptions2g"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectChannel')
                            }
                          }]"
                          :placeholder="t('Config.Mode.SelectChannel')"
                        />
                      </div>
                      <!-- 2.4G信道带宽选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Bandwidth') }}
                        </div>
                        <AppSelect
                          v-model="selectedBandwidth"
                          :items="BAND_WIDTH_2G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectBandwidth')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectBandwidth')"
                        />
                      </div>
                      <!-- 2.4G发射功率选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.TxPower') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiTxpower_2G"
                          :items="TX_POWER_2G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (!v && v !== '') {
                              return t('Config.Mode.SelectTxPower')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectTxPower')"
                        />
                      </div>
                      <!-- 2.4G最大连接数输入 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Device.AP.MaxConnections') }}
                        </div>
                        <AppTextField v-model="templateForm.wifiMaxsta_2G" />
                      </div>

                      <!-- 多SSID配置卡片 - 2.4G -->
                      <div v-if="templateForm.ssidConfigType === '1'">
                        <!-- 2.4G SSID 1 -->
                        <VCard
                          class="mb-4"
                          variant="outlined"
                        >
                          <VCardTitle class="d-flex justify-space-between align-center">
                            <span>{{ t('Config.Mode.SSID2G1') }}</span>
                          </VCardTitle>
                          <VCardText>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.SSID') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.ssid_2g"
                                :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                                :placeholder="t('Config.Mode.EnterSSID')"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.Password') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.key_2g"
                                :placeholder="t('Config.Mode.EnterPassword')"
                                :rules="[validatePasswordEightMore]"
                                :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                                :type="show2GPassword ? 'text' : 'password'"
                                @click:append-inner="show2GPassword = !show2GPassword"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.VLANID') }}
                              </div>
                              <AppSelect
                                v-model="templateForm.vlanTemplateId24G1"
                                :items="vlanList"
                                item-title="itemTitle"
                                item-value="id"
                                :placeholder="t('Config.Mode.SelectVLAN')"
                                @update:model-value="(value: any) => handleVlanSelect('vlanId24G1', value)"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.MaxConnections') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.wifiMaxsta_2G"
                                :placeholder="t('Config.Mode.EnterMaxConnections')"
                                type="number"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.APIsolation') }}
                              </div>
                              <VSwitch
                                v-model="templateForm.wifiApIsolate_2G"
                                false-value="0"
                                true-value="1"
                                :label="templateForm.wifiApIsolate_2G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                              />
                            </div>
                          </VCardText>
                        </VCard>

                        <!-- 2.4G SSID 2 -->
                        <VCard
                          v-if="ssid2gCount >= 2"
                          class="mb-4"
                          variant="outlined"
                        >
                          <VCardTitle class="d-flex justify-space-between align-center">
                            <span>{{ t('Config.Mode.SSID2G2') }}</span>
                            <VBtn
                              color="error"
                              size="small"
                              variant="text"
                              icon="tabler-trash"
                              @click="removeSSID2G"
                            />
                          </VCardTitle>
                          <VCardText>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.SSID') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.ssid_2g2"
                                :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                                :placeholder="t('Config.Mode.EnterSSID')"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.Password') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.key_2g2"
                                :placeholder="t('Config.Mode.EnterPassword')"
                                :rules="[validatePasswordEightMore]"
                                :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                                :type="show2GPassword ? 'text' : 'password'"
                                @click:append-inner="show2GPassword = !show2GPassword"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.VLANID') }}
                              </div>
                              <AppSelect
                                v-model="templateForm.vlanTemplateId24G2"
                                :items="vlanList"
                                item-title="itemTitle"
                                item-value="id"
                                :placeholder="t('Config.Mode.SelectVLAN')"
                                @update:model-value="(value: any) => handleVlanSelect('vlanId24G2', value)"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.MaxConnections') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.wifiMaxsta_2G2"
                                :placeholder="t('Config.Mode.EnterMaxConnections')"
                                type="number"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.APIsolation') }}
                              </div>
                              <VSwitch
                                v-model="templateForm.wifiApIsolate_2G2"
                                false-value="0"
                                true-value="1"
                                :label="templateForm.wifiApIsolate_2G2 === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                              />
                            </div>
                          </VCardText>
                        </VCard>
                      </div>
                      <!-- 2.4G客户端隔离开关（仅默认配置分开模式显示） -->
                      <div
                        v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'"
                        class="d-flex justify-space-between align-start mb-4"
                      >
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Isolate') }}
                        </div>
                        <VSwitch
                          v-model="templateForm.wifiApIsolate_2G"
                          false-value="0"
                          true-value="1"
                          :label="templateForm.wifiApIsolate_2G === '0' ? t('Config.Mode.Off') : t('Config.Mode.On')"
                        />
                      </div>
                      <VDivider class="mb-4" />
                      <!-- 5G -->
                      <div class="d-flex justify-space-between align-center mb-4">
                        <div class="text-primary text-h5">
                          {{ t('Config.Mode.WirelessSettings5G') }}
                        </div>
                        <VBtn
                          v-if="templateForm.ssidConfigType === '1' && ssid5gCount < 2"
                          color="primary"
                          size="small"
                          variant="outlined"
                          @click="addSSID5G"
                        >
                          {{ t('Config.Mode.AddSSID') }}
                        </VBtn>
                      </div>

                      <!-- 默认配置 - 5G分开模式 -->
                      <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'">
                        <!-- 5G WiFi开关 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EnableWiFi') }}
                          </div>
                          <div class="d-flex align-center">
                            <VSwitch
                              v-model="templateForm.wifiOnOff_5G"
                              class="mr-2"
                              false-value="1"
                              true-value="0"
                            />
                            <span class="text-subtitle-2 text-on-surface opacity-50">
                              {{
                                templateForm.wifiOnOff_5G === "0" ? t('Config.Mode.On') : t('Config.Mode.Off')
                              }}
                            </span>
                          </div>
                        </div>
                        <!-- 5G SSID名称输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_5g"
                            :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <!-- 5G加密类型选择 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EncryptionType') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.wifiAuthmode_5G"
                            :items="ENCRYPTION_TYPE_LOCALIZED"
                            :rules="templateForm.ssid_type === '0' ? [(v: string) => {
                              if (v === null) {
                                return t('Config.Mode.SelectEncryption')
                              }
                            }] : []"
                            item-title="label"
                            item-value="value"
                            :placeholder="t('Config.Mode.SelectEncryption')"
                          />
                        </div>
                        <!-- 5G密码输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_5g"
                            :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :rules="[validatePasswordEight5G]"
                            :type="show5GPassword ? 'text' : 'password'"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            @click:append-inner="show5GPassword = !show5GPassword"
                          />
                        </div>
                      </div>

                      <!-- 多SSID配置 - 5G WiFi开关 -->
                      <div v-if="templateForm.ssidConfigType === '1'">
                        <!-- 5G WiFi总开关 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EnableWiFi') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiOnOff_5G"
                            false-value="1"
                            true-value="0"
                            :label="templateForm.wifiOnOff_5G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </div>

                      <!-- 通用5G设置 -->
                      <!-- 5G协议选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Protocol') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiHwMode_5G"
                          :items="PROTOCOL_5G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectProtocol')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectProtocol')"
                        />
                      </div>
                      <!-- 5G国家码选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Country') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiCountry_5G"
                          :items="COUNTRY_OPTIONS_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectCountry')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectCountry')"
                          @update:model-value="() => {
                            templateForm.wifiChannel_5G = 'auto'
                          }"
                        />
                      </div>
                      <!-- 5G信道选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Channel') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiChannel_5G"
                          :items="channelOptions5g"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectChannel')
                            }
                          }]"
                          :placeholder="t('Config.Mode.SelectChannel')"
                          @update:model-value="changeChannel"
                        />
                      </div>
                      <!-- 5G信道带宽选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Bandwidth') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiHtMode_5G"
                          :items="BAND_WIDTH_5G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectBandwidth')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectBandwidth')"
                        />
                      </div>
                      <!-- 5G发射功率选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.TxPower') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiTxpower_5G"
                          :items="TX_POWER_5G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (!v && v !== '') {
                              return t('Config.Mode.SelectTxPower')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectTxPower')"
                        />
                      </div>
                      <!-- 5G最大连接数输入 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Device.AP.MaxConnections') }}
                        </div>
                        <AppTextField v-model="templateForm.wifiMaxsta_5G" />
                      </div>

                      <!-- 多SSID配置卡片 - 5G -->
                      <div v-if="templateForm.ssidConfigType === '1'">
                        <!-- 5G SSID 1 -->
                        <VCard
                          class="mb-4"
                          variant="outlined"
                        >
                          <VCardTitle class="d-flex justify-space-between align-center">
                            <span>{{ t('Config.Mode.SSID5G1') }}</span>
                          </VCardTitle>
                          <VCardText>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.SSID') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.ssid_5g"
                                :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                                :placeholder="t('Config.Mode.EnterSSID')"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.Password') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.key_5g"
                                :placeholder="t('Config.Mode.EnterPassword')"
                                :rules="[validatePasswordEight5GMore]"
                                :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                                :type="show5GPassword ? 'text' : 'password'"
                                @click:append-inner="show5GPassword = !show5GPassword"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.VLANID') }}
                              </div>
                              <AppSelect
                                v-model="templateForm.vlanTemplateId5G1"
                                :items="vlanList"
                                item-title="itemTitle"
                                item-value="id"
                                :placeholder="t('Config.Mode.SelectVLAN')"
                                @update:model-value="(value: any) => handleVlanSelect('vlanId5G1', value)"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.MaxConnections') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.wifiMaxsta_5G"
                                :placeholder="t('Config.Mode.EnterMaxConnections')"
                                type="number"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.APIsolation') }}
                              </div>
                              <VSwitch
                                v-model="templateForm.wifiApIsolate_5G"
                                false-value="0"
                                true-value="1"
                                :label="templateForm.wifiApIsolate_5G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                              />
                            </div>
                          </VCardText>
                        </VCard>

                        <!-- 5G SSID 2 -->
                        <VCard
                          v-if="ssid5gCount >= 2"
                          class="mb-4"
                          variant="outlined"
                        >
                          <VCardTitle class="d-flex justify-space-between align-center">
                            <span>{{ t('Config.Mode.SSID5G2') }}</span>
                            <VBtn
                              color="error"
                              size="small"
                              variant="text"
                              icon="tabler-trash"
                              @click="removeSSID5G"
                            />
                          </VCardTitle>
                          <VCardText>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.SSID') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.ssid_5g2"
                                :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                                :placeholder="t('Config.Mode.EnterSSID')"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.Password') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.key_5g2"
                                :placeholder="t('Config.Mode.EnterPassword')"
                                :rules="[validatePasswordEight5GMore]"
                                :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                                :type="show5GPassword ? 'text' : 'password'"
                                @click:append-inner="show5GPassword = !show5GPassword"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.VLANID') }}
                              </div>
                              <AppSelect
                                v-model="templateForm.vlanTemplateId5G2"
                                :items="vlanList"
                                item-title="itemTitle"
                                item-value="id"
                                :placeholder="t('Config.Mode.SelectVLAN')"
                                @update:model-value="(value: any) => handleVlanSelect('vlanId5G2', value)"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.MaxConnections') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.wifiMaxsta_5G2"
                                :placeholder="t('Config.Mode.EnterMaxConnections')"
                                type="number"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.APIsolation') }}
                              </div>
                              <VSwitch
                                v-model="templateForm.wifiApIsolate_5G2"
                                false-value="0"
                                true-value="1"
                                :label="templateForm.wifiApIsolate_5G2 === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                              />
                            </div>
                          </VCardText>
                        </VCard>
                      </div>

                      <!-- 5G客户端隔离开关（仅默认配置分开模式显示） -->
                      <div
                        v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type == '0'"
                        class="d-flex justify-space-between align-start mb-4"
                      >
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Isolate') }}
                        </div>
                        <div class="d-flex align-center">
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_5G"
                            class="mr-2"
                            false-value="0"
                            true-value="1"
                          />
                          <span class="text-subtitle-2 text-on-surface opacity-50">
                            {{
                              templateForm.wifiApIsolate_5G === "0" ? t('Config.Mode.Off') : t('Config.Mode.On')
                            }}
                          </span>
                        </div>
                      </div>
                    </VForm>
                  </VWindowItem>
                  <VWindowItem
                    :value="2"
                    class="flex-grow-1 overflow-auto"
                    style=" -ms-overflow-style: none;scrollbar-width: none;"
                  >
                    <!-- 性能优化：延迟加载标签页内容 -->
                    <VForm v-if="tabContentLoaded[2]">
                      <!-- 网络类型选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.NetworkType') }}
                        </div>
                        <AppSelect
                          v-model="workForm.net_type"
                          :items="NET_TYPE_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectNetworkType')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectNetworkType')"
                        />
                      </div>

                      <!-- VLAN配置（仅在网络类型为VLAN时显示） -->
                      <div
                        v-if="workForm.net_type === '2'"
                        class="mb-4"
                      >
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="workForm.vlanTemplateId"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            :rules="[(v: string) => {
                              if (!v) {
                                return t('Config.Mode.SelectVLAN')
                              }
                              return true
                            }]"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId', value)"
                          />
                        </div>
                      </div>
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Device.AP.LANIP') }}
                        </div>
                        <AppTextField
                          v-model="workForm.ap_lan_ip"
                          :placeholder="t('Device.AP.Required')"
                        />
                      </div>

                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('NetworkConfig.LAN.SubnetMask') }}
                        </div>
                        <AppSelect
                          v-model="workForm.ap_lan_mask"
                          :items="subnetMaskOptions"
                          :placeholder="t('NetworkConfig.LAN.EnterSubnetMask')"
                          item-title="label"
                          item-value="value"
                        />
                      </div>

                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.SpeedLimit') }}
                        </div>
                        <AppSelect
                          v-model="workForm.networkLimit"
                          :items="SPEED_LIMIT_TYPE_LIST"
                          disabled
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectSpeedLimit')"
                        />
                      </div>
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.UpstreamLimit') }}
                        </div>
                        <AppTextField
                          v-model="workForm.upstreamLimit"
                          disabled
                          :placeholder="t('Config.Mode.EnterUpstreamLimit')"
                        />
                      </div>
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.DownstreamLimit') }}
                        </div>
                        <AppTextField
                          v-model="workForm.upstreamLimit"
                          disabled
                          :placeholder="t('Config.Mode.EnterDownstreamLimit')"
                        />
                      </div>
                    </VForm>
                  </VWindowItem>
                </VWindow>
              </div>
            </div>
          </PerfectScrollbar>
        </div>

        <!-- 底部固定区域 -->
        <VDivider />
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-space-between">
          <VBtn
            color="primary"
            variant="outlined"
            @click="remoteConfigFun(activeAP)"
          >
            {{ t('Device.AP.RemoteConfig') }}
          </VBtn>
          <div>
            <VBtn
              class="mr-4"
              color="secondary"
              variant="tonal"
              @click="drawer = false"
            >
              {{ t('Device.AP.Cancel') }}
            </VBtn>
            <VBtn
              color="primary"
              @click="saveConfig"
            >
              {{ t('Device.AP.Save') }}
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style lang="scss" scoped>
.flexBox {
  display: flex;
  align-items: center;
}

.cardTitle {
  font-family: "PingFang SC", serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 28px;
}

.ap-manager {
  .label {
    color: #999;
    font-size: 13px;
    margin-block-end: 2px;
  }

  .value {
    color: rgba($color: var(--v-theme-on-surface), $alpha: 90%);
    font-size: 15px;
  }

  .sub-title {
    color: rgba($color: var(--v-theme-on-surface), $alpha: 55%);
    font-size: 15px;
    font-weight: normal;
  }

  .rounded-progress {
    border-end-start-radius: 10px !important;
    border-start-start-radius: 10px !important;
  }

  .h-38 {
    block-size: 38px;
    line-height: 38px;
  }

  .w-80 {
    inline-size: 80px;
  }
}

.w-80px {
  inline-size: 80px;
}

.line-height-38px {
  line-height: 38px;
}
</style>
