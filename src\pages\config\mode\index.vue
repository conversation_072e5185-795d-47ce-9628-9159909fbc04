<script lang="ts" setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import {
  BAND_WIDTH_2G,
  CHANNEL_ARR_2G,
  CHANNEL_ARR_5G,
  COUNTRY_OPTIONS,
  ENCRYPTION_TYPE,
  NET_TYPE,
  PROTOCOL_2G,
  PROTOCOL_5G,
  ROAMING_PROTOCOL,
  SPEED_LIMIT_TYPE,
  TX_POWER_2G,
  TX_POWER_5G,
} from '@/utils/constants'

import { isEmptyArray, isNullOrUndefined } from '@core/utils/helpers'
import { requiredValidator } from '@core/utils/validators'
import { isByteLengthInRange } from '@layouts/utils'

const { t } = useI18n()
const router = useRouter()
const selectedRows = ref([])

const itemsPerPage = ref(10)
const page = ref(1)
const dialogStatus = ref(0)

// 表头
const headers = [
  { title: t('Config.Mode.TemplateName'), key: 'name', sortable: false },
  { title: t('Config.Mode.ApplicableModel'), key: 'model', sortable: false },
  { title: t('Config.Mode.Wi-Fi'), key: 'config', sortable: false },
  { title: t('Config.Mode.CreationTime'), key: 'utime', sortable: false },
  { title: t('Config.Mode.DeviceCount'), key: 'deviceNum', sortable: false },
  { title: t('Config.Mode.VLANNum'), key: 'vlanNum', sortable: false }, // 新增
  { title: t('Config.Mode.Description'), key: 'remark', sortable: false },
  { title: t('Config.Mode.Actions'), key: 'actions', sortable: false },
]

// 动态计算表格数据
const templateList = ref<any[]>([])
const totalTemplates = ref(0)

const resetList = () => {
  page.value = 1
  getTemplateList()
}

const getTemplateList = () => {
  $get('v1/apTemplate', { page: page.value, size: itemsPerPage.value }).then((res: any) => {
    if (res.msg === 'success') {
      templateList.value = res.result.rows || []
      totalTemplates.value = res.result.count || 0
    }
    else {
      ElMessage.error(t('Config.Mode.GetTemplatesFailed'))
    }
  })
}

watch([page, itemsPerPage], () => {
  getTemplateList()
})

// 新建模版
const createTemplateDialog = ref(false)

const deviceDetail = (item: any) => {
  router.push({
    name: 'config-device',
    query: {
      tpl_id: item.id,
      name: item.tpl_name,
    },
  })
}

const editTemplate = (item: any) => {
  dialogStatus.value = 1 // 编辑状态
  openDialog()

  // 字段映射，参考 save
  templateForm.tpl_name = item.name || ''
  templateForm.description = item.remark || ''

  // 只赋值 templateForm 里有的字段
  const config = item.config || {}
  for (const key in templateForm) {
    if (Object.prototype.hasOwnProperty.call(item, key) && item[key] !== undefined) {
      if (key === 'model' && item[key] === undefined)
        continue;
      (templateForm as any)[key] = item[key]
    }
    else if (Object.prototype.hasOwnProperty.call(config, key) && config[key] !== undefined) {
      (templateForm as any)[key] = config[key]
    }
  }
  const ssidConfigType = templateForm.ssidConfigType

  // 保留原有的 SSID 计数、带宽等逻辑
  const {
    ssid_type,
    ssid_2g,
    ssid_2g2,
    ssid_5g2,
    wifiAuthmode_2G,
    key_2g,
    wifiApIsolate_2G,
    wifi80211r_2G,
    wifiHtMode_2G,
    wifiForce40MHzMode_2G,
  } = config

  if (ssidConfigType === '1') {
    ssid2gCount.value = ssid_2g2 ? 2 : 1
    ssid5gCount.value = ssid_5g2 ? 2 : 1
  }

  if (ssid_type == '1') {
    templateForm.ssid = ssid_2g
    templateForm.encryptionType = wifiAuthmode_2G
    templateForm.password = key_2g
    templateForm.isolate = wifiApIsolate_2G == '1'
  }
  if (wifi80211r_2G == '1') {
    templateForm.quickRoaming = true
    templateForm.roamingProtocol = ROAMING_PROTOCOL[0].value
  }
  if (wifiHtMode_2G == 'HT40') {
    if (wifiForce40MHzMode_2G == 0)
      selectedBandwidth.value = '20/40M'
    else
      selectedBandwidth.value = '40M'
  }
  else {
    selectedBandwidth.value = '20M'
  }

  // 设置各个VLAN模板ID
  templateForm.vlanTemplateId = item.vlanId || ''
  templateForm.vlanTemplateId24G1 = item.vlanId24G1 || ''
  templateForm.vlanTemplateId24G2 = item.vlanId24G2 || ''
  templateForm.vlanTemplateId5G1 = item.vlanId5G1 || ''
  templateForm.vlanTemplateId5G2 = item.vlanId5G2 || ''
  templateForm.vlanId = item.config.vlanId || ''
  templateForm.vlanId24G1 = item.config.vlanId24G1 || ''
  templateForm.vlanId24G2 = item.config.vlanId24G2 || ''
  templateForm.vlanId5G1 = item.config.vlanId5G1 || ''
  templateForm.vlanId5G2 = item.config.vlanId5G2 || ''
  templateForm.ap_lan_ip = item.config.ap_lan_ip || ''
  templateForm.ap_lan_mask = item.config.ap_lan_mask || ''
}

const createNewTemplate = () => {
  if (actionType.value === '0') {
    dialogStatus.value = 0 // 新建状态
    openDialog()
  }
  else {

  }
}

const openDialog = () => {
  resetForm()
  createTemplateDialog.value = true
  currentTab.value = 0
}

const resetForm = () => {
  // 重置SSID计数
  ssid2gCount.value = 1
  ssid5gCount.value = 1

  templateForm = reactive({
    id: '', // （随机生成32位以内可含英文或者数字或下划线的字符串，传值），模板的唯一识别码,id不可变，通过id绑定模板
    tpl_name: '', // 模板名称，支持中文（UTF-8）一个中文占三字节
    model: modelList.value.length > 0 ? String(modelList.value[0].value) : '', // AP型号，默认选中第一个
    tpl_bands: '', // 适用频段
    ssid_count: '', // SSID数量
    modified_at: '', //	最后修改时间
    description: '', //	备注，支持中文（UTF-8）一个中文占三字节
    ssid_2g: '', //	2G SSID
    ssid_5g: '', //	5G SSID
    key_2g: '', //	2G密码
    key_5g: '', //	5G密码
    ssid_type: '0', //	1：SSID双频合一 0：SSID分开
    ssidConfigType: '0', // 0：默认配置 1：多SSID配置
    net_type: '1', //	0：路由模式 1：AP模式，默认AP模式
    ap_lan_ip: '', //	LAN IP
    ap_lan_mask: '', //	LAN子网掩码
    wifiOnOff_2G: '0', // 2.4GWiFi开关 0：开启 1：关闭
    wifiOnOff_5G: '0', // 5.8GWiFi开关 0：开启 1：关闭
    wifiApIsolate_2G: '0', //	2.4G AP隔离  0：关闭 1：开启
    wifiApIsolate_5G: '0', //	5.8G AP隔离  0：关闭 1：开启
    wifiEnable_2G: '0', //	2.4G SSID 隐藏  0：关闭 1：开启
    wifiEnable_5G: '0', //	5.8G SSID 隐藏  0：关闭 1：开启
    wifiCountry_2G: 'CN', //	2.4G 国家代码例如中国传值CN
    wifiCountry_5G: 'CN', //	5.8G 国家代码
    wifiChannel_2G: 'auto', //	2.4G 信道
    wifiChannel_5G: 'auto', //	5.8G 信道
    wifiHwMode_2G: '11axg', //	2.4G协议，默认802.11ax
    wifiHwMode_5G: '11axa', //	5.8G协议，默认802.11ax
    wifiHtMode_2G: 'HT20', //	2.4G带宽
    wifiForce40MHzMode_2G: '0', //	2.4G强制带宽40M选项
    wifiHtMode_5G: 'HT160', //	5.8G带宽
    wifiTxpower_2G: '', // 2.4G信号调节
    wifiTxpower_5G: '', // 5.8G信号调节
    wifi80211r_2G: '', //	2.4G快速漫游r协议
    wifi80211r_5G: '', //	5.8G快速漫游r协议
    wifiAuthmode_2G: 'mixed-psk', //	2.4G加密方式选项，默认wpa1/wpa2
    wifiAuthmode_5G: 'mixed-psk', //	5.8G加密方式选项，默认wpa1/wpa2
    wifiWpa3_2G: '', //	2.4G wpa3 开启标志
    wifiWpa3_5G: '', //	5.8G wpa3 开启标志

    // 多SSID配置字段
    ssid_2g2: '', // 2.4G SSID 2
    key_2g2: '', // 2.4G 密码 2
    vlanId24G1: '', // 2.4G SSID 1 VLAN ID
    vlanId24G2: '', // 2.4G SSID 2 VLAN ID
    vlanTemplateId24G1: '', // 2.4G SSID 1 VLAN模板ID
    vlanTemplateId24G2: '', // 2.4G SSID 2 VLAN模板ID
    wifiMaxsta_2G2: '', // 2.4G SSID 2 最大连接数
    wifiApIsolate_2G2: '0', // 2.4G SSID 2 AP隔离
    ssid_5g2: '', // 5G SSID 2
    key_5g2: '', // 5G 密码 2
    vlanId5G1: '', // 5G SSID 1 VLAN ID
    vlanId5G2: '', // 5G SSID 2 VLAN ID
    vlanTemplateId5G1: '', // 5G SSID 1 VLAN模板ID
    vlanTemplateId5G2: '', // 5G SSID 2 VLAN模板ID
    wifiMaxsta_5G2: '', // 5G SSID 2 最大连接数
    wifiApIsolate_5G2: '0', // 5G SSID 2 AP隔离
    // 双频合一的参数
    ssid: '', // SSID名称
    encryptionType: 'mixed-psk', // 加密类型，默认wpa1/wpa2
    password: '', // 密码
    isolate: false, // 隔离
    // 漫游配置
    quickRoaming: false, // 快速漫游
    roamingProtocol: undefined, // 漫游协议
    // 以下是暂不使用的变量
    networkLimit: undefined, // 限速
    upstreamLimit: '', // 上行限制
    downstreamLimit: '', // 下行限制
    wifiMaxsta_2G: '',
    wifiMaxsta_5G: '',
    vlanNum: 0, // VLAN模板数量
    vlanId: '', // VLAN ID
    vlanTemplateId: '', // VLAN模板ID
    load_balance_interval: '', // SSID负载均衡间隔
    weak_signal_threshold: '', // 信号阙值
    ignore_weak_signal: '', // 忽略弱信号STA
    ignore_excessive_retransmission: '', // 忽略重传过多STA
  })
}

const copyTemplate = (item: any) => {
  const new_temp = JSON.parse(JSON.stringify(item))

  $post(`/v1/apTemplateClone/${item.id}`, {
    name: `${new_temp.name}-copy`,
  }).then((res: any) => {
    if (res.msg === 'success')
      resetList()

    else
      ElMessage.error(res.msg)
  })

  // resetList()
}

const deleteTemplate = (item: any) => {
  if (item.id) {
    ElMessageBox.confirm(
      t('Config.Mode.DeleteConfirm', { name: item.name }),
      t('Config.Mode.Tips'),
      {
        confirmButtonText: t('Config.Mode.Confirm'),
        cancelButtonText: t('Config.Mode.Cancel'),
        type: 'warning',
      },
    ).then(() => {
      $delete(`/v1/apTemplate/${item.id}`, {}).then((res: any) => {
        if (res.msg === 'success') {
          ElMessage.success(t('Config.Mode.DeleteSuccess'))
          resetList()
        }
        else {
          ElMessage.error(res.msg)
        }
      })
    })
  }
}

const currentTab = ref(0)

const tabList = ref([
  { label: t('Config.Mode.BasicSettings'), value: 0 },
  { label: t('Config.Mode.SSIDConfig'), value: 1 },
  { label: t('Config.Mode.AdvancedConfig'), value: 2 },
  { label: t('Config.Mode.RoamingConfig'), value: 3 },
])

const formRef1 = ref()
const formRef2 = ref()
const formRef3 = ref()
const formRef4 = ref()
const formRef = [formRef1, formRef2, formRef3, formRef4]

// 使用从constants.ts直接导入的ROAMING_PROTOCOL

const showMergePassword = ref(false)
const show2GPassword = ref(false)
const show5GPassword = ref(false)
let templateForm = reactive({
  id: '', // （随机生成32位以内可含英文或者数字或下划线的字符串，传值），模板的唯一识别码,id不可变，通过id绑定模板
  tpl_name: '', // 模板名称，支持中文（UTF-8）一个中文占三字节
  model: '', // AP型号，默认选中第一个
  tpl_bands: '', // 适用频段
  ssid_count: '', // SSID数量
  modified_at: '', //	最后修改时间
  description: '', //	备注，支持中文（UTF-8）一个中文占三字节
  ssid_2g: '', //	2G SSID
  ssid_5g: '', //	5G SSID
  key_2g: '', //	2G密码
  key_5g: '', //	5G密码
  ssid_type: '0', //	1：SSID双频合一 0：SSID分开
  ssidConfigType: '0', // 0：默认配置 1：多SSID配置
  net_type: '1', //	0：路由模式 1：AP模式，默认AP模式
  ap_lan_ip: '', //	LAN IP
  ap_lan_mask: '', //	LAN子网掩码
  wifiOnOff_2G: '0', // 2.4GWiFi开关 0：开启 1：关闭
  wifiOnOff_5G: '0', // 5.8GWiFi开关 0：开启 1：关闭
  wifiApIsolate_2G: '0', //	2.4G AP隔离  0：关闭 1：开启
  wifiApIsolate_5G: '0', //	5.8G AP隔离  0：关闭 1：开启
  wifiEnable_2G: '0', //	2.4G SSID 隐藏  0：关闭 1：开启
  wifiEnable_5G: '0', //	5.8G SSID 隐藏  0：关闭 1：开启
  wifiCountry_2G: 'CN', //	2.4G 国家代码例如中国传值CN
  wifiCountry_5G: 'CN', //	5.8G 国家代码
  wifiChannel_2G: 'auto', //	2.4G 信道
  wifiChannel_5G: 'auto', //	5.8G 信道
  wifiHwMode_2G: '11axg', //	2.4G协议，默认802.11ax
  wifiHwMode_5G: '11axa', //	5.8G协议，默认802.11ax
  wifiHtMode_2G: 'HT20', //	2.4G带宽
  wifiForce40MHzMode_2G: '0', //	2.4G强制带宽40M选项
  wifiHtMode_5G: 'HT160', //	5.8G带宽
  wifiTxpower_2G: '', // 2.4G信号调节
  wifiTxpower_5G: '', // 5.8G信号调节
  wifi80211r_2G: '', //	2.4G快速漫游r协议
  wifi80211r_5G: '', //	5.8G快速漫游r协议
  wifiAuthmode_2G: 'mixed-psk', //	2.4G加密方式选项，默认wpa1/wpa2
  wifiAuthmode_5G: 'mixed-psk', //	5.8G加密方式选项，默认wpa1/wpa2
  wifiWpa3_2G: '', //	2.4G wpa3 开启标志
  wifiWpa3_5G: '', //	5.8G wpa3 开启标志
  // 多SSID配置字段
  ssid_2g2: '', // 2.4G SSID 2
  key_2g2: '', // 2.4G 密码 2
  vlanId24G1: '', // 2.4G SSID 1 VLAN ID
  vlanId24G2: '', // 2.4G SSID 2 VLAN ID
  vlanTemplateId24G1: '', // 2.4G SSID 1 VLAN模板ID
  vlanTemplateId24G2: '', // 2.4G SSID 2 VLAN模板ID
  wifiMaxsta_2G2: '', // 2.4G SSID 2 最大连接数
  wifiApIsolate_2G2: '0', // 2.4G SSID 2 AP隔离
  ssid_5g2: '', // 5G SSID 2
  key_5g2: '', // 5G 密码 2
  vlanId5G1: '', // 5G SSID 1 VLAN ID
  vlanId5G2: '', // 5G SSID 2 VLAN ID
  vlanTemplateId5G1: '', // 5G SSID 1 VLAN模板ID
  vlanTemplateId5G2: '', // 5G SSID 2 VLAN模板ID
  wifiMaxsta_5G2: '', // 5G SSID 2 最大连接数
  wifiApIsolate_5G2: '0', // 5G SSID 2 AP隔离
  // 双频合一的参数
  ssid: '', // SSID名称
  encryptionType: 'mixed-psk', // 加密类型，默认wpa1/wpa2
  password: '', // 密码
  isolate: false, // 隔离
  // 漫游配置
  quickRoaming: false, // 快速漫游
  roamingProtocol: undefined, // 漫游协议
  // 以下是暂不使用的变量
  networkLimit: undefined, // 限速
  upstreamLimit: '', // 上行限制
  downstreamLimit: '', // 下行限制
  wifiMaxsta_2G: '',
  wifiMaxsta_5G: '',
  vlanNum: 0, // VLAN模板数量
  // VLAN配置字段
  vlanId: '', // VLAN ID (1-4094)
  vlanTemplateId: '', // VLAN模板ID
  load_balance_interval: '', // SSID负载均衡间隔
  weak_signal_threshold: '', // 信号阙值
  ignore_weak_signal: '', // 忽略弱信号STA
  ignore_excessive_retransmission: '', // 忽略重传过多STA
})

const channelOptions2g = computed(() => {
  const index = COUNTRY_OPTIONS.findIndex((data: any) => data.value === templateForm.wifiCountry_2G)

  return CHANNEL_ARR_2G[index] || []
})

const channelOptions5g = computed(() => {
  const index = COUNTRY_OPTIONS.findIndex((data: any) => data.value === templateForm.wifiCountry_5G)

  return CHANNEL_ARR_5G[index] || []
})

// 直接监听channelOptions的变化
watch(templateForm.wifiCountry_2G, () => {
  templateForm.wifiChannel_2G = 'auto'
}, { immediate: true })

watch(templateForm.wifiCountry_5G, () => {
  templateForm.wifiChannel_5G = 'auto'
}, { immediate: true })

const nextStep = () => {
  formRef[currentTab.value].value?.validate().then(res => {
    if (res.valid)
      currentTab.value += 1
  })
}

const validAllForm = async () => {
  for (let i = 0; i < formRef.length; i++) {
    const validator = formRef[i]
    const valid = await validator.value?.validate()

    if (!valid?.valid) {
      currentTab.value = i

      return false
    }
  }

  return true
}

const ssidTypeChange = () => {
  formRef2.value?.resetValidation()
}

const ssidConfigTypeChange = () => {
  formRef2.value?.resetValidation()

  // 切换到多SSID配置时，重置双频合一为分开模式
  if (templateForm.ssidConfigType === '1')
    templateForm.ssid_type = '0'
}

const save = async () => {
  if (!(await validAllForm()))
    return

  // 先执行VLAN计算
  calculateVlanNum()

  // 等待VLAN计算完成后再进行数据提取和提交
  await nextTick()

  let {
    id,
    tpl_name,
    tpl_bands,
    model,
    ssid_count,
    description,
    ssid_2g,
    ssid_5g,
    key_2g,
    key_5g,
    ssid_type,
    ssidConfigType,
    net_type,
    ap_lan_ip,
    ap_lan_mask,
    wifiOnOff_2G,
    wifiOnOff_5G,
    wifiApIsolate_2G,
    wifiApIsolate_5G,
    wifiEnable_2G,
    wifiEnable_5G,
    wifiCountry_2G,
    wifiCountry_5G,
    wifiChannel_2G,
    wifiChannel_5G,
    wifiHwMode_2G,
    wifiHwMode_5G,
    wifiHtMode_2G,
    wifiForce40MHzMode_2G,
    wifiHtMode_5G,
    wifiTxpower_2G,
    wifiTxpower_5G,
    wifi80211r_2G,
    wifi80211r_5G,
    wifiAuthmode_2G,
    wifiAuthmode_5G,
    wifiWpa3_2G,
    wifiWpa3_5G,
    ssid,
    encryptionType,
    password,
    isolate,
    quickRoaming,
    roamingProtocol,
    wifiMaxsta_2G,
    wifiMaxsta_5G,

    // 多SSID配置字段
    ssid_2g2,
    key_2g2,

    wifiMaxsta_2G2,
    wifiApIsolate_2G2,
    ssid_5g2,
    key_5g2,

    wifiMaxsta_5G2,
    wifiApIsolate_5G2,
    vlanNum,
    vlanId,
    vlanId24G1,
    vlanId24G2,
    vlanId5G1,
    vlanId5G2,
    vlanTemplateId,
    vlanTemplateId24G1,
    vlanTemplateId24G2,
    vlanTemplateId5G1,
    vlanTemplateId5G2,
    load_balance_interval,
    weak_signal_threshold,
    ignore_weak_signal,
    ignore_excessive_retransmission,
  } = templateForm

  const vlan = vlanList.value.find(v => v.id === vlanTemplateId)
  const vlan24G1 = vlanList.value.find(v => v.id === vlanTemplateId24G1)
  const vlan24G2 = vlanList.value.find(v => v.id === vlanTemplateId24G2)
  const vlan5G1 = vlanList.value.find(v => v.id === vlanTemplateId5G1)
  const vlan5G2 = vlanList.value.find(v => v.id === vlanTemplateId5G2)

  vlanId = vlan?.vlanId || ''
  vlanId24G1 = vlan24G1?.vlanId || ''
  vlanId24G2 = vlan24G2?.vlanId || ''
  vlanId5G1 = vlan5G1?.vlanId || ''
  vlanId5G2 = vlan5G2?.vlanId || ''

  // 处理默认配置的双频合一
  if (ssidConfigType === '0' && ssid_type == '1') {
    ssid_2g = ssid
    ssid_5g = ssid
    key_2g = password
    key_5g = password
    wifiApIsolate_2G = isolate ? '1' : '0'
    wifiApIsolate_5G = isolate ? '1' : '0'
    wifiAuthmode_2G = encryptionType
    wifiAuthmode_5G = encryptionType
    tpl_bands = '2.4G/5G'
  }
  else {
    // 计算频段信息
    const bands = []
    if (wifiOnOff_2G == '0')
      bands.push('2.4G')
    if (wifiOnOff_5G == '0')
      bands.push('5G')
    tpl_bands = bands.length > 0 ? bands.join('/') : '-'
  }

  // 处理多SSID配置的SSID数量
  if (ssidConfigType === '1') {
    let count = 0
    if (wifiOnOff_2G == '0')
      count += ssid2gCount.value
    if (wifiOnOff_5G == '0')
      count += ssid5gCount.value
    ssid_count = count.toString()
  }
  else {
    ssid_count = '1'
  }

  if (quickRoaming && roamingProtocol === 0) {
    wifi80211r_2G = '1'
    wifi80211r_5G = '1'
  }
  else {
    wifi80211r_2G = '0'
    wifi80211r_5G = '0'
  }

  if (wifiAuthmode_2G === 'psk2')
    wifiWpa3_2G = '1'
  else
    wifiWpa3_2G = '0'

  if (wifiAuthmode_5G === 'psk2')
    wifiWpa3_5G = '1'
  else
    wifiWpa3_5G = '0'

  if (wifiAuthmode_2G == 'none')
    key_2g = ''

  if (wifiAuthmode_5G == 'none')
    key_5g = ''

  let postData = {}
  if (ssidConfigType === '1') {
    if (dialogStatus == 1) {
      postData = {
        id,
        name: tpl_name,
        model,
        remark: description,
        vlanId: vlanTemplateId || '',
        vlanId24G1: '', // 2.4G1 VLAN模板ID
        vlanId24G2: '', // 2.4G2 VLAN模板ID
        vlanId5G1: '', // 5G1 VLAN模板ID
        vlanId5G2: '', // 5G2 VLAN模板ID
        vlanNum,
        ssidConfigType,
        config: {
          tpl_bands,
          ssid_count,
          ssid_2g,
          ssid_5g,
          key_2g,
          key_5g,
          ssid_type,
          net_type,
          ap_lan_ip,
          ap_lan_mask,
          wifiOnOff_2G,
          wifiOnOff_5G,
          wifiApIsolate_2G,
          wifiApIsolate_5G,
          wifiEnable_2G,
          wifiEnable_5G,
          wifiCountry_2G,
          wifiCountry_5G,
          wifiChannel_2G,
          wifiChannel_5G,
          wifiHwMode_2G,
          wifiHwMode_5G,
          wifiHtMode_2G,
          wifiForce40MHzMode_2G,
          wifiHtMode_5G,
          wifiTxpower_2G,
          wifiTxpower_5G,
          wifi80211r_2G,
          wifi80211r_5G,
          wifiAuthmode_2G,
          wifiAuthmode_5G,
          wifiWpa3_2G,
          wifiWpa3_5G,
          wifiMaxsta_2G,
          wifiMaxsta_5G,
          vlanId: '',
          vlanId24G1: '',
          vlanId24G2: '',
          vlanId5G1: '',
          vlanId5G2: '',
          ssid_2g2: '',
          key_2g2: '',
          wifiMaxsta_2G2: '',
          wifiApIsolate_2G2: '',
          ssid_5g2: '',
          key_5g2: '',
          wifiMaxsta_5G2: '',
          wifiApIsolate_5G2: '',
          vlanTemplateId,
          vlanTemplateId24G1,
          vlanTemplateId24G2,
          vlanTemplateId5G1,
          vlanTemplateId5G2,
          load_balance_interval,
          weak_signal_threshold,
          ignore_weak_signal,
          ignore_excessive_retransmission,
        },
      }
    }
    else {
      postData = {
        name: tpl_name,
        model,
        remark: description,
        vlanId: vlanTemplateId || '',
        vlanId24G1: vlanTemplateId24G1, // 2.4G1 VLAN模板ID
        vlanId24G2: vlanTemplateId24G2, // 2.4G2 VLAN模板ID
        vlanId5G1: vlanTemplateId5G1, // 5G1 VLAN模板ID
        vlanId5G2: vlanTemplateId5G2, // 5G2 VLAN模板ID
        vlanNum,
        ssidConfigType,
        config: {
          tpl_bands,
          ssid_count,
          ssid_2g,
          ssid_5g,
          key_2g,
          key_5g,
          ssid_type,
          net_type,
          ap_lan_ip,
          ap_lan_mask,
          wifiOnOff_2G,
          wifiOnOff_5G,
          wifiApIsolate_2G,
          wifiApIsolate_5G,
          wifiEnable_2G,
          wifiEnable_5G,
          wifiCountry_2G,
          wifiCountry_5G,
          wifiChannel_2G,
          wifiChannel_5G,
          wifiHwMode_2G,
          wifiHwMode_5G,
          wifiHtMode_2G,
          wifiForce40MHzMode_2G,
          wifiHtMode_5G,
          wifiTxpower_2G,
          wifiTxpower_5G,
          wifi80211r_2G,
          wifi80211r_5G,
          wifiAuthmode_2G,
          wifiAuthmode_5G,
          wifiWpa3_2G,
          wifiWpa3_5G,
          wifiMaxsta_2G,
          wifiMaxsta_5G,
          vlanId: vlanId || '',
          vlanId24G1: '',
          vlanId24G2: '',
          vlanId5G1: '',
          vlanId5G2: '',
          ssid_2g2: '',
          key_2g2: '',
          wifiMaxsta_2G2: '',
          wifiApIsolate_2G2: '',
          ssid_5g2: '',
          key_5g2: '',
          wifiMaxsta_5G2: '',
          wifiApIsolate_5G2: '',
          vlanTemplateId,
          vlanTemplateId24G1,
          vlanTemplateId24G2,
          vlanTemplateId5G1,
          vlanTemplateId5G2,
          load_balance_interval,
          weak_signal_threshold,
          ignore_weak_signal,
          ignore_excessive_retransmission,
        },
      }
    }
  }
  else {
    if (dialogStatus == 1) {
      postData = {
        id,
        name: tpl_name,
        model,
        remark: description,
        vlanId: vlanTemplateId || '',
        vlanId24G1: vlanTemplateId24G1, // 2.4G1 VLAN模板ID
        vlanId24G2: vlanTemplateId24G2, // 2.4G2 VLAN模板ID
        vlanId5G1: vlanTemplateId5G1, // 5G1 VLAN模板ID
        vlanId5G2: vlanTemplateId5G2, // 5G2 VLAN模板ID
        vlanNum,
        ssidConfigType,
        config: {
          tpl_bands,
          ssid_count,
          ssid_2g,
          ssid_5g,
          key_2g,
          key_5g,
          ssid_type,
          net_type,
          ap_lan_ip,
          ap_lan_mask,
          wifiOnOff_2G,
          wifiOnOff_5G,
          wifiApIsolate_2G,
          wifiApIsolate_5G,
          wifiEnable_2G,
          wifiEnable_5G,
          wifiCountry_2G,
          wifiCountry_5G,
          wifiChannel_2G,
          wifiChannel_5G,
          wifiHwMode_2G,
          wifiHwMode_5G,
          wifiHtMode_2G,
          wifiForce40MHzMode_2G,
          wifiHtMode_5G,
          wifiTxpower_2G,
          wifiTxpower_5G,
          wifi80211r_2G,
          wifi80211r_5G,
          wifiAuthmode_2G,
          wifiAuthmode_5G,
          wifiWpa3_2G,
          wifiWpa3_5G,
          wifiMaxsta_2G,
          wifiMaxsta_5G,
          vlanId,
          vlanId24G1,
          vlanId24G2,
          vlanId5G1,
          vlanId5G2,
          ssid_2g2,
          key_2g2,
          wifiMaxsta_2G2,
          wifiApIsolate_2G2,
          ssid_5g2,
          key_5g2,
          wifiMaxsta_5G2,
          wifiApIsolate_5G2,
          vlanTemplateId,
          vlanTemplateId24G1,
          vlanTemplateId24G2,
          vlanTemplateId5G1,
          vlanTemplateId5G2,
          load_balance_interval,
          weak_signal_threshold,
          ignore_weak_signal,
          ignore_excessive_retransmission,
        },
      }
    }
    else {
      postData = {
        name: tpl_name,
        model,
        remark: description,
        vlanId: vlanTemplateId || '',
        vlanId24G1: vlanTemplateId24G1, // 2.4G1 VLAN模板ID
        vlanId24G2: vlanTemplateId24G2, // 2.4G2 VLAN模板ID
        vlanId5G1: vlanTemplateId5G1, // 5G1 VLAN模板ID
        vlanId5G2: vlanTemplateId5G2, // 5G2 VLAN模板ID
        vlanNum,
        ssidConfigType,
        config: {
          tpl_bands,
          ssid_count,
          ssid_2g,
          ssid_5g,
          key_2g,
          key_5g,
          ssid_type,
          net_type,
          ap_lan_ip,
          ap_lan_mask,
          wifiOnOff_2G,
          wifiOnOff_5G,
          wifiApIsolate_2G,
          wifiApIsolate_5G,
          wifiEnable_2G,
          wifiEnable_5G,
          wifiCountry_2G,
          wifiCountry_5G,
          wifiChannel_2G,
          wifiChannel_5G,
          wifiHwMode_2G,
          wifiHwMode_5G,
          wifiHtMode_2G,
          wifiForce40MHzMode_2G,
          wifiHtMode_5G,
          wifiTxpower_2G,
          wifiTxpower_5G,
          wifi80211r_2G,
          wifi80211r_5G,
          wifiAuthmode_2G,
          wifiAuthmode_5G,
          wifiWpa3_2G,
          wifiWpa3_5G,
          wifiMaxsta_2G,
          wifiMaxsta_5G,
          vlanId,
          vlanId24G1,
          vlanId24G2,
          vlanId5G1,
          vlanId5G2,
          ssid_2g2,
          key_2g2,
          wifiMaxsta_2G2,
          wifiApIsolate_2G2,
          ssid_5g2,
          key_5g2,
          wifiMaxsta_5G2,
          wifiApIsolate_5G2,
          vlanTemplateId,
          vlanTemplateId24G1,
          vlanTemplateId24G2,
          vlanTemplateId5G1,
          vlanTemplateId5G2,
          load_balance_interval,
          weak_signal_threshold,
          ignore_weak_signal,
          ignore_excessive_retransmission,
        },
      }
    }
  }
  console.log(postData)
  if (id) {
  // 使用新的postData结构进行提交
    $put(`/v1/apTemplate/${id}`, postData).then(res => {
      if (res.msg === 'success') {
        ElMessage.success(t('Config.Mode.SaveSuccess'))
        createTemplateDialog.value = false
        resetForm()
        resetList()
      }
      else {
        ElMessage.error(t('Config.Mode.SaveFailed'))
      }
    })
  }
  else {
  // 使用新的postData结构进行提交
    $post('/v1/apTemplate', postData).then(res => {
      if (res.msg === 'success') {
        ElMessage.success(t('Config.Mode.SaveSuccess'))
        createTemplateDialog.value = false
        resetForm()
        resetList()
      }
      else {
        ElMessage.error(t('Config.Mode.SaveFailed'))
      }
    })
  }
}

const modelList = ref()

const getApList = () => {
  $get('/v1/apModel', { }).then(res => {
    if (res.msg === 'success') {
      // 添加调试日志
      console.log('API响应:', res)

      const models = res.result || []

      console.log('提取的型号:', Array.from(models))

      // 先加上"请选择"选项
      modelList.value = [
        ...Array.from(models).map(item => ({
          label: String(item),
          value: String(item),
        })),
      ]

      console.log('modelList:', modelList.value)
    }
  }).catch(err => {
    console.error('获取AP列表失败:', err)
  })
}

const formatTime = (time: string) => {
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

onMounted(async () => {
  getApList()
  getTemplateList()
  getVlanList()
})

const vlanList = ref<any[]>([])

const getVlanList = () => {
  $get('/v1/vlanConfigs', { page: 1, size: 1000 }).then((res: any) => {
    if (res.msg === 'success' || res.msg === 'success') {
      // 兼容不同后端返回结构
      const rows = res.result?.rows || []

      vlanList.value = [
        { id: '', vlanId: '' },
        ...rows,
      ]

      vlanList.value.forEach(item => {
        if (item.id)
          item.itemTitle = `${item.name} VLAN ${item.vlanId} ${item.vlanId}`

        else
          item.itemTitle = t('PleaseSelect')
      })
    }
    else {
      ElMessage.error(res.err_message || res.msg || '获取VLAN列表失败')
    }
  })
}

// 计算VLAN模板数量
const calculateVlanNum = () => {
  const vlanIds = new Set()

  // 收集所有使用的VLAN模板ID，空值不参与计算
  if (templateForm.vlanTemplateId24G1 && templateForm.vlanTemplateId24G1 !== '')
    vlanIds.add(templateForm.vlanTemplateId24G1)
  if (templateForm.vlanTemplateId24G2 && templateForm.vlanTemplateId24G2 !== '')
    vlanIds.add(templateForm.vlanTemplateId24G2)
  if (templateForm.vlanTemplateId5G1 && templateForm.vlanTemplateId5G1 !== '')
    vlanIds.add(templateForm.vlanTemplateId5G1)
  if (templateForm.vlanTemplateId5G2 && templateForm.vlanTemplateId5G2 !== '')
    vlanIds.add(templateForm.vlanTemplateId5G2)
  if (templateForm.vlanTemplateId && templateForm.vlanTemplateId !== '')
    vlanIds.add(templateForm.vlanTemplateId)

  console.log(vlanIds, templateForm)
  templateForm.vlanNum = vlanIds.size
}

// VLAN选择处理函数
const handleVlanSelect = () => {
}

// 对constants中的国际化常量进行本地化处理
const COUNTRY_OPTIONS_LOCALIZED = computed(() => {
  return COUNTRY_OPTIONS.map(item => ({
    ...item,
    label: item.label, // 国家代码不需要翻译
  }))
})

const ENCRYPTION_TYPE_LOCALIZED = computed(() => {
  return ENCRYPTION_TYPE.map(item => ({
    ...item,
    label: item.label === 'None' ? t('Config.Mode.None') : item.label, // 只有None需要翻译，其他是技术标准
  }))
})

const BAND_WIDTH_2G_LOCALIZED = computed(() => {
  return BAND_WIDTH_2G.map(item => ({
    ...item,
    label: item.label, // 带宽值不需要翻译
  }))
})

const PROTOCOL_2G_LOCALIZED = computed(() => {
  return PROTOCOL_2G.map(item => ({
    ...item,
    label: item.label, // 协议不需要翻译
  }))
})

const PROTOCOL_5G_LOCALIZED = computed(() => {
  return PROTOCOL_5G.map(item => ({
    ...item,
    label: item.label, // 协议不需要翻译
  }))
})

const TX_POWER_2G_LOCALIZED = computed(() => {
  return TX_POWER_2G.map(item => {
    if (item.label.startsWith('txPower.')) {
      return {
        ...item,
        label: t(item.label), // 发射功率需要翻译
      }
    }

    return item
  })
})

const TX_POWER_5G_LOCALIZED = computed(() => {
  return TX_POWER_5G.map(item => {
    if (item.label.startsWith('txPower.')) {
      return {
        ...item,
        label: t(item.label), // 发射功率需要翻译
      }
    }

    return item
  })
})

const NET_TYPE_LOCALIZED = computed(() => {
  return NET_TYPE.map(item => {
    if (item.label.startsWith('NetworkConfig.Modes.')) {
      return {
        ...item,
        label: t(item.label), // 网络类型需要翻译
      }
    }

    return item
  })
})

// 对SPEED_LIMIT_TYPE进行本地化处理
const SPEED_LIMIT_TYPE_LIST = computed(() => {
  return SPEED_LIMIT_TYPE.map(item => ({
    ...item,
    label: t(item.label), // 国家代码不需要翻译
  }))
})

const selectedBandwidth = ref('20M')

watch(selectedBandwidth, newVal => {
  switch (newVal) {
    case '40M':
      templateForm.wifiHtMode_2G = 'HT40'
      templateForm.wifiForce40MHzMode_2G = '1'
      break
    case '20/40M':
      templateForm.wifiHtMode_2G = 'HT40'
      templateForm.wifiForce40MHzMode_2G = '0'
      break
    case '20M':
    default:
      templateForm.wifiHtMode_2G = 'HT20'
      templateForm.wifiForce40MHzMode_2G = '0'
      break
  }
}, { immediate: true })

const validatePassword = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.encryptionType === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight = (value: string) => {
  // 默认配置且双频合一时不需要验证分开的密码
  if (templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1' || templateForm.ssidConfigType === '1')
    return true

  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_2G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEightMore = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_2G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight5G = (value: string) => {
  // 默认配置且双频合一时不需要验证分开的密码
  if (templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1' || templateForm.ssidConfigType === '1')
    return true

  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_5G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight5GMore = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_5G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

// 新增：根据5G信道动态计算可用的带宽选项
const BAND_WIDTH_5G_LOCALIZED = computed(() => {
  const channel = templateForm.wifiChannel_5G

  // 默认情况或自动信道：提供所有带宽选项
  if (!channel || channel === 'auto') {
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }

  const channelNum = Number.parseInt(channel, 10)

  // 根据信道范围返回相应的带宽选项
  if (channelNum >= 36 && channelNum <= 128) {
    // 36-128 可选20, 40, 80, 160
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }
  else if (channelNum === 132 || channelNum === 136) {
    // 132、136 可选20, 40
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
    ]
  }
  else if (channelNum === 140 || channelNum === 144) {
    // 140、144只能选20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
  else if (channelNum >= 149 && channelNum <= 161) {
    // 149-161 可选 20, 40, 80
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
    ]
  }
  else {
    // 161以上可选 20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
})

const changeChannel = () => {
  const newBandWidthOptions = BAND_WIDTH_5G_LOCALIZED.value
  const currentValue = templateForm.wifiHtMode_5G
  const isValidOption = newBandWidthOptions.some(option => option.value === currentValue)

  if (!isValidOption && newBandWidthOptions.length > 0) {
    // 如果当前选择不可用，则默认选择列表中第一个选项
    templateForm.wifiHtMode_5G = newBandWidthOptions[0].value
  }
}

const requiredValidatorNew = (value: unknown, message: string) => {
  // 默认配置且双频合一时不需要验证分开的SSID
  if (templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1' || templateForm.ssidConfigType === '1')
    return true

  if (isNullOrUndefined(value) || isEmptyArray(value) || value === false)
    return 'This field is required'

  return !!String(value).trim().length || message || 'This field is required'
}

const filterType = ref('0')

const filterTypeList = ref([
  {
    label: 'AP',
    value: '0',
  },
  {
    label: 'AC',
    value: '1',
  },
])

const actionTypeList = computed(() => [
  { label: t('Config.Mode.NewAPTemplate'), value: '0' },
  { label: t('Config.Mode.NewACTemplate'), value: '1' },
])

const actionType = ref('0')

// SSID配置类型选项列表
const ssidConfigTypeList = ref([
  {
    label: t('Config.Mode.DefaultConfig'),
    value: '0',
  },
  {
    label: t('Config.Mode.MultiSSID'),
    value: '1',
  },
])

// 多SSID配置相关状态
const ssid2gCount = ref(1) // 2.4G SSID数量，默认1个
const ssid5gCount = ref(1) // 5G SSID数量，默认1个

// 增加SSID
const addSSID2G = () => {
  if (ssid2gCount.value < 2) {
    ssid2gCount.value++
  }
}

const addSSID5G = () => {
  if (ssid5gCount.value < 2) {
    ssid5gCount.value++
  }
}

// 删除SSID
const removeSSID2G = () => {
  if (ssid2gCount.value > 1) {
    ssid2gCount.value--

    // 清空第二个SSID的数据
    templateForm.ssid_2g2 = ''
    templateForm.key_2g2 = ''
    templateForm.vlanId24G2 = ''
    templateForm.wifiMaxsta_2G2 = ''
    templateForm.wifiApIsolate_2G2 = '0'
  }
}

const removeSSID5G = () => {
  if (ssid5gCount.value > 1) {
    ssid5gCount.value--

    // 清空第二个SSID的数据
    templateForm.ssid_5g2 = ''
    templateForm.key_5g2 = ''
    templateForm.vlanId5G2 = ''
    templateForm.wifiMaxsta_5G2 = ''
    templateForm.wifiApIsolate_5G2 = '0'
  }
}

// 子网掩码选项列表
const subnetMaskOptions = [
  // A类网络常用
  { label: '255.0.0.0/8', value: '255.0.0.0' },
  { label: '255.128.0.0/9', value: '255.128.0.0' },
  { label: '255.192.0.0/10', value: '255.192.0.0' },
  { label: '255.224.0.0/11', value: '255.224.0.0' },
  { label: '255.240.0.0/12', value: '255.240.0.0' },
  { label: '255.248.0.0/13', value: '255.248.0.0' },
  { label: '255.252.0.0/14', value: '255.252.0.0' },
  { label: '255.254.0.0/15', value: '255.254.0.0' },

  // B类网络常用
  { label: '255.255.0.0/16', value: '255.255.0.0' },
  { label: '255.255.128.0/17', value: '255.255.128.0' },
  { label: '255.255.192.0/18', value: '255.255.192.0' },
  { label: '255.255.224.0/19', value: '255.255.224.0' },
  { label: '255.255.240.0/20', value: '255.255.240.0' },
  { label: '255.255.248.0/21', value: '255.255.248.0' },
  { label: '255.255.252.0/22', value: '255.255.252.0' },
  { label: '255.255.254.0/23', value: '255.255.254.0' },

  // C类网络常用
  { label: '255.255.255.0/24', value: '255.255.255.0' },
  { label: '***************/25', value: '***************' },
  { label: '***************/26', value: '***************' },
  { label: '***************/27', value: '***************' },
  { label: '***************/28', value: '***************' },
  { label: '***************/29', value: '***************' },
  { label: '***************/30', value: '***************' },

  // 特殊用途
  { label: '***************/31', value: '***************' },
  { label: '***************/32', value: '***************' },
]
</script>

<template>
  <div>
    <VCard class="mb-6">
      <template #title>
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
        >
          <div class="mr-2">
            {{ t('Config.Mode.TemplateManagement') }}
            <BtnGroupSelector
              v-model:value="filterType"
              class="ml-5"
              fill-row
              :options="filterTypeList"
            />
          </div>

          <div class="gap-4 d-flex flex-wrap">
            <!-- 新建配置模板按钮改为带菜单的按钮 -->
            <VMenu>
              <template #activator="{ props }">
                <VBtn
                  color="success"
                  prepend-icon="tabler-layout-grid-add"
                  variant="tonal"
                  v-bind="props"
                >
                  {{ t('Config.Mode.NewConfigTemplate') }}
                </VBtn>
              </template>
              <VList>
                <VListItem
                  v-for="item in actionTypeList"
                  :key="item.value"
                  @click="() => { actionType = item.value; createNewTemplate(); }"
                >
                  {{ item.label }}
                </VListItem>
              </VList>
            </VMenu>
          </div>
        </div>
      </template>

      <VDivider />

      <VDataTableServer
        v-model:items-per-page="itemsPerPage"
        v-model:model-value="selectedRows"
        v-model:page="page"
        :headers="headers"
        :items="templateList"
        :items-length="totalTemplates"
        :no-data-text="t('NoData')"
        class="text-no-wrap"
      >
        <template #item.config="{ item }">
          <div
            v-if="item.config"
            class="d-flex align-center"
          >
            <VChip
              class="mr-2"
              color="success"
              size="small"
              variant="outlined"
            >
              {{ item.config.tpl_bands }}
            </VChip>
            {{ item.config.ssid_2g || '--' }} /{{ item.config.ssid_5g || '--' }}
          </div>
          <VChip
            v-else
            color="error"
            size="small"
            variant="outlined"
          >
            {{ t('Config.Mode.Off') }}
          </VChip>
        </template>
        <template #item.model="{ item }">
          {{ item.model ? item.model : "--" }}
        </template>
        <template #item.utime="{ item }">
          {{ item.utime ? formatTime(item.utime) : "--" }}
        </template>
        <template #item.deviceNum="{ item }">
          <div
            class="text-primary cursor-pointer associated-device"
            @click="deviceDetail(item)"
          >
            {{ item.associated_devices || 0 }}
          </div>
        </template>
        <template #item.vlanNum="{ item }">
          {{ item.vlanNum || 0 }}
        </template>
        <template #item.actions="{ item }">
          <IconBtn>
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem @click="editTemplate(item)">
                  {{ t('Config.Mode.EditTemplate') }}
                </VListItem>
                <VListItem @click="copyTemplate(item)">
                  {{ t('Config.Mode.CopyTemplate') }}
                </VListItem>
                <VListItem @click="deleteTemplate(item)">
                  {{ t('Config.Mode.DeleteTemplate') }}
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalTemplates"
          />
        </template>
      </VDataTableServer>
    </VCard>

    <!-- 新建模板 -->
    <VNavigationDrawer
      v-if="createTemplateDialog"
      v-model="createTemplateDialog"
      persistent
      location="right"
      temporary
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ dialogStatus === 1 ? t('Config.Mode.EditTemplate') : t('Config.Mode.NewConfigTemplate') }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="createTemplateDialog = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间内容区 - flex-grow-1确保占据剩余空间 -->
        <div class="flex-grow-1 d-flex flex-column overflow-hidden">
          <!-- 标签页固定 -->
          <div class="flex-shrink-0 px-6 pt-4">
            <VTabs
              v-model="currentTab"
              class="mb-4"
              align-tabs="center"
              fixed-tabs
            >
              <VTab
                v-for="(item, index) in tabList"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </VTab>
            </VTabs>
          </div>

          <!-- 内容区可滚动 -->
          <div class="flex-grow-1 px-6 pb-4 overflow-y-auto hide-scrollbar">
            <VWindow v-model="currentTab">
              <!-- 选择型号 -->
              <VWindowItem :value="0">
                <VForm ref="formRef1">
                  <!-- 模板名称输入框 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TemplateName') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.tpl_name"
                      :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterTemplateName')), (v: string) => {
                        if (!isByteLengthInRange(v, 1, 64)) {
                          return t('Config.Mode.TemplateNameLengthError')
                        }
                      }]"
                      :placeholder="t('Config.Mode.EnterTemplateName')"
                    />
                  </div>
                  <!-- 备注输入框 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Remark') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.description"
                      :rules="[(v: string) => {
                        if (!isByteLengthInRange(v, 0, 128)) {
                          return t('Config.Mode.RemarkLengthError')
                        }
                      }]"
                      :placeholder="t('Config.Mode.SelectRemark')"
                    />
                  </div>
                  <!-- AP型号选择下拉框 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.SelectModel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.model"
                      class="mb-4"
                      :placeholder="t('Config.Mode.SelectAPModel')"
                      :items="modelList"
                      item-title="label"
                      item-value="value"
                    />
                  </div>
                </VForm>
              </VWindowItem>
              <!-- 基础配置 -->
              <VWindowItem :value="1">
                <VForm ref="formRef2">
                  <!-- SSID配置类型选择按钮组 -->
                  <div class="d-flex justify-space-between align-center mb-4">
                    <BtnGroupSelector
                      v-model:value="templateForm.ssidConfigType"
                      fill-row
                      :options="ssidConfigTypeList"
                      @update:value="ssidConfigTypeChange"
                    />
                  </div>

                  <!-- 双频合一开关 (仅默认配置显示) -->
                  <div
                    v-if="templateForm.ssidConfigType === '0'"
                    class="d-flex justify-space-between align-center mb-4"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90">
                      {{ t('Config.AP.DualBandUnify') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.ssid_type"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                        @update:model-value="ssidTypeChange"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{ t('Config.AP.DualBandUnifyHint') }}
                      </span>
                    </div>
                  </div>
                  <!-- 默认配置 - 双频合一 -->
                  <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1'">
                    <!-- 双频合一SSID名称输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.Mode.EnterSSID')"
                      />
                    </div>
                    <!-- 双频合一加密类型选择 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.encryptionType"
                        :items="ENCRYPTION_TYPE_LOCALIZED"
                        :placeholder="t('Config.Mode.SelectEncryption')"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.SelectEncryption'))]"
                        item-title="label"
                        item-value="value"
                      />
                    </div>
                    <!-- 双频合一密码输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.password"
                        :append-inner-icon="showMergePassword ? 'tabler-eye-off' : 'tabler-eye'"
                        :placeholder="t('Config.Mode.EnterPassword')"
                        :rules="[validatePassword]"
                        :type="showMergePassword ? 'text' : 'password'"
                        @click:append-inner="
                          showMergePassword = !showMergePassword
                        "
                      />
                    </div>
                    <!-- 双频合一客户端隔离开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Isolate') }}
                      </div>
                      <div class="d-flex align-center">
                        <VSwitch
                          v-model="templateForm.isolate"
                          class="mr-2"
                        />
                        <span class="text-subtitle-2 text-on-surface opacity-50">
                          {{ templateForm.isolate ? t('Config.Mode.On') : t('Config.Mode.Off') }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <VDivider class="mb-4" />
                  <div class="d-flex justify-space-between align-center mb-4">
                    <div class="text-primary text-h5">
                      {{ t('Config.Mode.WirelessSettings2G') }}
                    </div>
                    <VBtn
                      v-if="templateForm.ssidConfigType === '1' && ssid2gCount < 2"
                      color="primary"
                      size="small"
                      variant="outlined"
                      @click="addSSID2G"
                    >
                      {{ t('Config.Mode.AddSSID') }}
                    </VBtn>
                  </div>

                  <!-- 默认配置 - 2.4G分开模式 -->
                  <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'">
                    <!-- 启用Wi-Fi开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <VSwitch
                        v-model="templateForm.wifiOnOff_2G"
                        false-value="1"
                        true-value="0"
                        :label="templateForm.wifiOnOff_2G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                      />
                    </div>
                    <!-- SSID名称输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid_2g"
                        :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.Mode.EnterSSID')"
                      />
                    </div>
                    <!-- 2.4G加密类型选择 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.wifiAuthmode_2G"
                        :items="ENCRYPTION_TYPE_LOCALIZED"
                        item-title="label"
                        item-value="value"
                        :placeholder="t('Config.Mode.SelectEncryption')"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.SelectEncryption'))]"
                      />
                    </div>
                    <!-- 2.4G密码输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.key_2g"
                        :placeholder="t('Config.Mode.EnterPassword')"
                        :rules="[validatePasswordEight]"
                        :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                        :type="show2GPassword ? 'text' : 'password'"
                        @click:append-inner="show2GPassword = !show2GPassword"
                      />
                    </div>
                  </div>

                  <!-- 多SSID配置 - 2.4G WiFi开关 -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 2.4G WiFi总开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <VSwitch
                        v-model="templateForm.wifiOnOff_2G"
                        false-value="1"
                        true-value="0"
                        :label="templateForm.wifiOnOff_2G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                      />
                    </div>
                  </div>

                  <!-- 通用2.4G设置 -->
                  <!-- 2.4G协议选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Protocol') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHwMode_2G"
                      :items="PROTOCOL_2G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectProtocol')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectProtocol')"
                    />
                  </div>
                  <!-- 2.4G国家码选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Country') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiCountry_2G"
                      :items="COUNTRY_OPTIONS_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectCountry')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectCountry')"
                      @update:model-value="() => {
                        templateForm.wifiChannel_2G = 'auto'
                      }"
                    />
                  </div>
                  <!-- 2.4G信道选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Channel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiChannel_2G"
                      :items="channelOptions2g"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectChannel')
                        }
                      }]"
                      :placeholder="t('Config.Mode.SelectChannel')"
                    />
                  </div>
                  <!-- 2.4G信道带宽选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Bandwidth') }}
                    </div>
                    <AppSelect
                      v-model="selectedBandwidth"
                      :items="BAND_WIDTH_2G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectBandwidth')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectBandwidth')"
                    />
                  </div>
                  <!-- 2.4G发射功率选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TxPower') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiTxpower_2G"
                      :items="TX_POWER_2G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (!v && v !== '') {
                          return t('Config.Mode.SelectTxPower')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectTxPower')"
                    />
                  </div>
                  <!-- 2.4G最大连接数输入 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Device.AP.MaxConnections') }}
                    </div>
                    <AppTextField v-model="templateForm.wifiMaxsta_2G" />
                  </div>

                  <!-- 多SSID配置卡片 - 2.4G -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 2.4G SSID 1 -->
                    <VCard
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID2G1') }}</span>
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_2g"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_2g"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEightMore]"
                            :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show2GPassword ? 'text' : 'password'"
                            @click:append-inner="show2GPassword = !show2GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId24G1"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId24G1', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_2G"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_2G"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_2G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>

                    <!-- 2.4G SSID 2 -->
                    <VCard
                      v-if="ssid2gCount >= 2"
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID2G2') }}</span>
                        <VBtn
                          color="error"
                          size="small"
                          variant="text"
                          icon="tabler-trash"
                          @click="removeSSID2G"
                        />
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_2g2"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_2g2"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEightMore]"
                            :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show2GPassword ? 'text' : 'password'"
                            @click:append-inner="show2GPassword = !show2GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId24G2"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId24G2', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_2G2"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_2G2"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_2G2 === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>
                  </div>
                  <!-- 2.4G客户端隔离开关（仅默认配置分开模式显示） -->
                  <div
                    v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'"
                    class="d-flex justify-space-between align-start mb-4"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Isolate') }}
                    </div>
                    <VSwitch
                      v-model="templateForm.wifiApIsolate_2G"
                      false-value="0"
                      true-value="1"
                      :label="templateForm.wifiApIsolate_2G === '0' ? t('Config.Mode.Off') : t('Config.Mode.On')"
                    />
                  </div>
                  <VDivider class="mb-4" />
                  <!-- 5G -->
                  <div class="d-flex justify-space-between align-center mb-4">
                    <div class="text-primary text-h5">
                      {{ t('Config.Mode.WirelessSettings5G') }}
                    </div>
                    <VBtn
                      v-if="templateForm.ssidConfigType === '1' && ssid5gCount < 2"
                      color="primary"
                      size="small"
                      variant="outlined"
                      @click="addSSID5G"
                    >
                      {{ t('Config.Mode.AddSSID') }}
                    </VBtn>
                  </div>

                  <!-- 默认配置 - 5G分开模式 -->
                  <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'">
                    <!-- 5G WiFi开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <div class="d-flex align-center">
                        <VSwitch
                          v-model="templateForm.wifiOnOff_5G"
                          class="mr-2"
                          false-value="1"
                          true-value="0"
                        />
                        <span class="text-subtitle-2 text-on-surface opacity-50">
                          {{
                            templateForm.wifiOnOff_5G === "0" ? t('Config.Mode.On') : t('Config.Mode.Off')
                          }}
                        </span>
                      </div>
                    </div>
                    <!-- 5G SSID名称输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid_5g"
                        :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.Mode.EnterSSID')"
                      />
                    </div>
                    <!-- 5G加密类型选择 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.wifiAuthmode_5G"
                        :items="ENCRYPTION_TYPE_LOCALIZED"
                        :rules="templateForm.ssid_type === '0' ? [(v: string) => {
                          if (v === null) {
                            return t('Config.Mode.SelectEncryption')
                          }
                        }] : []"
                        item-title="label"
                        item-value="value"
                        :placeholder="t('Config.Mode.SelectEncryption')"
                      />
                    </div>
                    <!-- 5G密码输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.key_5g"
                        :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                        :rules="[validatePasswordEight5G]"
                        :type="show5GPassword ? 'text' : 'password'"
                        :placeholder="t('Config.Mode.EnterPassword')"
                        @click:append-inner="show5GPassword = !show5GPassword"
                      />
                    </div>
                  </div>

                  <!-- 多SSID配置 - 5G WiFi开关 -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 5G WiFi总开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <VSwitch
                        v-model="templateForm.wifiOnOff_5G"
                        false-value="1"
                        true-value="0"
                        :label="templateForm.wifiOnOff_5G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                      />
                    </div>
                  </div>

                  <!-- 通用5G设置 -->
                  <!-- 5G协议选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Protocol') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHwMode_5G"
                      :items="PROTOCOL_5G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectProtocol')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectProtocol')"
                    />
                  </div>
                  <!-- 5G国家码选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Country') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiCountry_5G"
                      :items="COUNTRY_OPTIONS_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectCountry')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectCountry')"
                      @update:model-value="() => {
                        templateForm.wifiChannel_5G = 'auto'
                      }"
                    />
                  </div>
                  <!-- 5G信道选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Channel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiChannel_5G"
                      :items="channelOptions5g"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectChannel')
                        }
                      }]"
                      :placeholder="t('Config.Mode.SelectChannel')"
                      @update:model-value="changeChannel"
                    />
                  </div>
                  <!-- 5G信道带宽选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Bandwidth') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHtMode_5G"
                      :items="BAND_WIDTH_5G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectBandwidth')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectBandwidth')"
                    />
                  </div>
                  <!-- 5G发射功率选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TxPower') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiTxpower_5G"
                      :items="TX_POWER_5G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (!v && v !== '') {
                          return t('Config.Mode.SelectTxPower')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectTxPower')"
                    />
                  </div>
                  <!-- 5G最大连接数输入 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Device.AP.MaxConnections') }}
                    </div>
                    <AppTextField v-model="templateForm.wifiMaxsta_5G" />
                  </div>

                  <!-- 多SSID配置卡片 - 5G -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 5G SSID 1 -->
                    <VCard
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID5G1') }}</span>
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_5g"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_5g"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEight5GMore]"
                            :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show5GPassword ? 'text' : 'password'"
                            @click:append-inner="show5GPassword = !show5GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId5G1"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId5G1', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_5G"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_5G"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_5G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>

                    <!-- 5G SSID 2 -->
                    <VCard
                      v-if="ssid5gCount >= 2"
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID5G2') }}</span>
                        <VBtn
                          color="error"
                          size="small"
                          variant="text"
                          icon="tabler-trash"
                          @click="removeSSID5G"
                        />
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_5g2"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_5g2"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEight5GMore]"
                            :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show5GPassword ? 'text' : 'password'"
                            @click:append-inner="show5GPassword = !show5GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId5G2"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId5G2', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_5G2"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_5G2"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_5G2 === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>
                  </div>

                  <!-- 5G客户端隔离开关（仅默认配置分开模式显示） -->
                  <div
                    v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type == '0'"
                    class="d-flex justify-space-between align-start mb-4"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Isolate') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.wifiApIsolate_5G"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{
                          templateForm.wifiApIsolate_5G === "0" ? t('Config.Mode.Off') : t('Config.Mode.On')
                        }}
                      </span>
                    </div>
                  </div>
                </VForm>
              </VWindowItem>
              <VWindowItem :value="2">
                <VForm ref="formRef3">
                  <!-- 网络类型选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.NetworkType') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.net_type"
                      :items="NET_TYPE_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectNetworkType')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectNetworkType')"
                    />
                  </div>

                  <!-- VLAN配置（仅在网络类型为VLAN时显示） -->
                  <div
                    v-if="templateForm.net_type === '2'"
                    class="mb-4"
                  >
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.VLANID') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.vlanTemplateId"
                        :items="vlanList"
                        item-title="itemTitle"
                        item-value="id"
                        :placeholder="t('Config.Mode.SelectVLAN')"
                        :rules="[(v: string) => {
                          if (!v) {
                            return t('Config.Mode.SelectVLAN')
                          }
                          return true
                        }]"
                        @update:model-value="(value: any) => handleVlanSelect('vlanId', value)"
                      />
                    </div>
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Device.AP.LANIP') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.ap_lan_ip"
                      :placeholder="t('Device.AP.Required')"
                    />
                  </div>

                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.LAN.SubnetMask') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.ap_lan_mask"
                      :items="subnetMaskOptions"
                      placeholder="t('NetworkConfig.LAN.EnterSubnetMask')"
                      item-title="label"
                      item-value="value"
                    />
                  </div>

                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.SpeedLimit') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.networkLimit"
                      :items="SPEED_LIMIT_TYPE_LIST"
                      disabled
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectSpeedLimit')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.UpstreamLimit') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.upstreamLimit"
                      disabled
                      :placeholder="t('Config.Mode.EnterUpstreamLimit')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.DownstreamLimit') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.downstreamLimit"
                      disabled
                      :placeholder="t('Config.Mode.EnterDownstreamLimit')"
                    />
                  </div>
                </VForm>
              </VWindowItem>
              <VWindowItem :value="3">
                <VForm ref="formRef4">
                  <!-- 快速漫游开关 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.QuickRoaming') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.quickRoaming"
                        class="mr-2"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{ templateForm.quickRoaming ? t('Config.Mode.On') : t('Config.Mode.Off') }}
                      </span>
                    </div>
                  </div>
                  <!-- 漫游协议选择（仅在快速漫游开启时显示） -->
                  <div v-if="templateForm.quickRoaming">
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.RoamingProtocol') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.roamingProtocol"
                        :items="ROAMING_PROTOCOL"
                        item-title="label"
                        item-value="value"
                        :placeholder="t('Config.Mode.SelectRoamingProtocol')"
                        :rules="[(v: number | undefined) => {
                          if (v === undefined) {
                            return t('Config.Mode.SelectRoamingProtocol')
                          }
                          return true
                        }]"
                      />
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSIDLoadBalancing') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.load_balance_interval"
                        class="mb-4"
                        :placeholder="t('Config.Mode.SSIDLoadBalancingPlaceholder')"
                      />
                    </div>

                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.DisconnectWeakSignal') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.weak_signal_threshold"
                        class="mb-4"
                        :placeholder="t('Config.Mode.DisconnectWeakSignalPlaceholder')"
                      />
                    </div>

                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.IgnoreWeakSignal') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ignore_weak_signal"
                        class="mb-4"
                        :placeholder="t('Config.Mode.IgnoreWeakSignalPlaceholder')"
                      />
                    </div>

                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.IgnoreExcessiveRetransmission') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ignore_excessive_retransmission"
                        :placeholder="t('Config.Mode.IgnoreExcessiveRetransmissionPlaceholder')"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.NumericRangeValidation'))]"
                      />
                    </div>
                  </div>
                </VForm>
              </VWindowItem>
            </VWindow>
          </div>
        </div>

        <!-- 底部固定 -->
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-end">
          <VBtn
            color="secondary"
            variant="tonal"
            class="mr-2"
            @click="createTemplateDialog = false"
          >
            {{ t('Config.Mode.Cancel') }}
          </VBtn>
          <VBtn
            v-if="currentTab !== tabList.length - 1"
            color="primary"
            variant="flat"
            @click="nextStep"
          >
            {{ t('Config.Mode.Next') }}
          </VBtn>
          <VBtn
            v-else
            color="primary"
            variant="flat"
            @click="save"
          >
            {{ t('Config.Mode.SaveTemplate') }}
          </VBtn>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style scoped>
.w-80px {
  inline-size: 80px;
}

.line-height-38px {
  line-height: 38px;
}

.flexBox {
  display: flex;
  align-items: center;
}

.associated-device {
  &:hover {
    text-decoration: underline;
  }
}

.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
</style>
