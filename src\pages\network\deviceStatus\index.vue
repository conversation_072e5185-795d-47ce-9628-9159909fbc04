<script lang="ts" setup>
import { useTheme } from 'vuetify'
import { hexToRgb } from '@core/utils/colorConverter'

const vuetifyTheme = useTheme()

const balanceChartConfig = computed(() => {
  return {
    chart: {
      type: 'area',
      parentHeightOffset: 0,
      zoom: { enabled: false },
      toolbar: { show: false },
    },
    dataLabels: { enabled: false },
    colors: ['#24B364', '#E64449'],
    stroke: {
      curve: 'straight',
      width: 1.5,
      lineCap: 'round',
    },
    legend: {
      show: false,
    },
    fill: {
      type: 'gradient',
      gradient: {
        type: 'vertical',
        opacityFrom: 0.3,
        opacityTo: 0.3,
        stops: [0, 100],
      },
    },
    tooltip: {
      custom(data: any) {
        console.log('data >', data)

        return `<div class='bar-chart pa-2'>
          <div>在线：${data.series[0][data.dataPointIndex]}</div>
          <div>离线：${data.series[1][data.dataPointIndex]}</div>
        </div>`
      },
    },
    grid: {
      padding: { top: -10 },
      strokeDashArray: 4,
      xaxis: {
        lines: { show: true },
      },
    },
    markers: {
      size: 2,
      strokeWidth: 1.5,
      strokeOpacity: 1,
      strokeColors: ['#24B364', '#E64449'],

      // colors: ['#fff', '#fff'],
      hover: {
        size: 3,
      },
    },
    xaxis: {
      axisBorder: { show: false },
      crosshairs: {
        stroke: {
          show: true,
          width: 1,
          color: '#B6B6B6',
          dashArray: 0,
        },
      },
      categories: [
        '7/12',
        '8/12',
        '9/12',
        '10/12',
        '11/12',
        '12/12',
        '13/12',
        '14/12',
        '15/12',
        '16/12',
        '17/12',
        '18/12',
        '19/12',
        '20/12',
        '21/12',
      ],
    },
  }
})

const series = [
  {
    name: 'online',
    data: [280, 200, 220, 180, 270, 250, 70, 90, 200, 150, 160, 100, 150, 100, 50],
  },
  {
    name: 'offline',
    data: [120, 100, 80, 90, 70, 60, 50, 40, 30, 20, 10, 0, 0, 0, 0],
  },
]

const date = ref(0)

const pieConfig = computed(() => {
  return {
    labels: ['在线', '离线'],
    stroke: {},
    dataLabels: {
      enabled: false,
    },
    tooltip: {
      custom({ series, seriesIndex, dataPointIndex, w }: any) {
        return `
            <div class="chart-tooltip">
              <p class="chart-tooltip-title mb-2">${w.globals.labels[seriesIndex]}</p>
              <p class="chart-tooltip-label">占比</p>
              <p class="chart-tooltip-value mb-2">${w.globals.seriesPercent[seriesIndex][0]}</p>
              <p class="chart-tooltip-label">终端数</p>
              <p class="chart-tooltip-value text-primary">${series[seriesIndex]}</p>
            </div>
          `
      },
    },
    colors: ['#28C76F', '#ff4b51'],
    plotOptions: {
      pie: {
        donut: {
          size: '85%',
          label: {
            show: false,
          },
        },
      },
    },
    legend: {
      position: 'bottom',
      formatter(seriesName: string, opts: any) {
        return `<span style="color:${opts.w.globals.colors[opts.seriesIndex]}">${seriesName}</span>`
      },
      itemMargin: {
        horizontal: 5,
        vertical: 0,
      },
      markers: {
        size: 4,
      },
    },
    annotations: {
      texts: [{
        x: '50%',
        y: '40%',
        text: '84',
        textAnchor: 'middle',
        fontSize: '28px',
        fontWeight: 'bold',
        foreColor: `rgba(${hexToRgb(vuetifyTheme.current.value.colors['on-surface'])}, 0.9)`,
      }, {
        x: '50%',
        y: '55%',
        text: '终端',
        textAnchor: 'middle',
        fontSize: '15px',
        fontWeight: 'bold',
        paddingRight: '50%',
        foreColor: `rgba(${hexToRgb(vuetifyTheme.current.value.colors['on-surface'])}, 0.7)`,
      }],
    },

  }
})

const seriesForRation = [80, 20]

const productsData = ref({
  total: 3,
  products: [
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: 0,
    },
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: 1,
    },
  ],
})

const headers = [
  { title: '设备名称', key: 'name' },
  { title: 'IP地址', key: 'ip' },
  { title: '设备型号', key: 'no' },
  { title: '当前状态', key: 'status' },
  { title: '软件版本', key: 'version' },
]

const totalProduct = computed(() => productsData.value.products.length + 100)
const page = ref(1)

const terminalStatus = ref(0)
const terminalNetworkType = ref(0)
</script>

<template>
  <div class="device-status">
    <VCard class="mb-6">
      <template #title>
        <div class="d-flex align-start justify-space-between">
          <div class="mr-2 text-h5">
            设备状态（86）
          </div>
          <div>
            <DateSelector
              v-model:current-value="date"
              :items="DATE_FILTER_OPTIONS"
              item-title="title"
              item-value="value"
            />
          </div>
        </div>
      </template>
      <VCardText>
        <div class="status-statistic mb-4">
          <div class="status-statistic-item success">
            <div class="label mb-2">
              设备在线数量
            </div>
            <div class="num">
              45
            </div>
          </div>
          <div class="status-statistic-item error">
            <div class="label mb-2">
              设备离线数量
            </div>
            <div class="num">
              45
            </div>
          </div>
          <div class="status-statistic-item">
            <div class="label mb-2">
              AC在线/总数
            </div>
            <div class="num text-success">
              <span>1</span>
              <span class="opacity-30	">/12</span>
            </div>
          </div>
          <div class="status-statistic-item">
            <div class="label mb-2">
              AP在线/总数
            </div>
            <div class="num text-success">
              <span>1</span>
              <span class="opacity-30	">/12</span>
            </div>
          </div>
        </div>
        <VRow>
          <VCol cols="3">
            <VueApexCharts
              class="custom-pie-chart"
              type="donut"
              height="250"
              :options="pieConfig"
              :series="seriesForRation"
            />
          </VCol>
          <VCol cols="9">
            <VueApexCharts
              type="area"
              height="250"
              :options="balanceChartConfig"
              :series="series"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard>
      <template #title>
        <div class="d-flex align-start">
          <div class="mr-6">
            终端连接时长排行
          </div>
          <BtnGroupSelector
            v-model:value="terminalStatus"
            class="mr-6"
            :options="TERMINAL_STATUS_FILTER_OPTIONS"
          />
          <BtnGroupSelector
            v-model:value="terminalNetworkType"
            :options="TERMINAL_NETWORK_TYPE_FILTER_OPTIONS"
          />
        </div>
      </template>
      <VDivider />
      <VDataTableServer
        :items="productsData.products"
        :headers="headers"
        :items-length="totalProduct"
        :no-data-text="t('NoData')"
      >
        <template #item.ip="{ item }">
          <span class="text-primary">{{ item.mac }}</span>
        </template>
        <template #item.status="{ item }">
          <span :class="[item.status === 0 ? 'text-success' : 'text-error']">{{ item.status === 0 ? '在线' : '离线'
          }}</span>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="10"
            :total-items="totalProduct"
            :show-meta="true"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </div>
</template>

<style lang="scss" scoped>
.device-status {
  .status-statistic {
    display: grid;
    column-gap: 10px;
    grid-template-columns: repeat(4, 1fr);

    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      background: rgba($color: var(--v-theme-on-surface), $alpha: 8%);
      block-size: 76px;
      inline-size: 100%;

      .label {
        color: rgba(var(--v-theme-on-surface), 0.9);
        font-size: 15px;
      }

      .num {
        color: rgb(var(--v-theme-on-surface), 0.9);
        font-size: 28px;
        font-weight: 500;
      }

      &.success {
        background: rgba($color: var(--v-theme-success), $alpha: 8%);

        .num {
          color: rgb(var(--v-theme-success));
        }
      }

      &.error {
        background: rgba($color: var(--v-theme-error), $alpha: 8%);

        .num {
          color: rgb(var(--v-theme-error));
        }
      }
    }
  }
}
</style>

<style lang="scss">
.custom-pie-chart {
  .apexcharts-legend {
    &-series {
      border: 1px solid rgba($color: var(--v-theme-on-surface), $alpha: 12%);
      border-radius: 6px;
      padding-block: 3px;
      padding-inline: 15px;
    }
  }
}
</style>
