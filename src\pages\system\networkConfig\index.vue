<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import DhcpPage from '@/components/system/networkConfig/dhcpPage.vue'
import IPPage from '@/components/system/networkConfig/IPPage.vue'
import LanPage from '@/components/system/networkConfig/lanPage.vue'
import NetworkPage from '@/components/system/networkConfig/networkPage.vue'
import PortPage from '@/components/system/networkConfig/portPage.vue'
import WanPage from '@/components/system/networkConfig/wanPage.vue'

const { t } = useI18n()

const activeTab = ref(0)

const tabList = ref([
  {
    text: t('NetworkConfig.Tabs.PortStatus'),
    value: 0,
  },
  {
    text: t('NetworkConfig.Tabs.NetworkSettings'),
    value: 1,
  },
  {
    text: t('NetworkConfig.Tabs.IPMapping'),
    value: 2,
  },
  {
    text: t('NetworkConfig.Tabs.PortMapping'),
    value: 3,
  },
  {
    text: t('NetworkConfig.Tabs.LANSettings'),
    value: 4,
  },
  {
    text: t('NetworkConfig.Tabs.DHCPIPBinding'),
    value: 5,
  },
])

const MODE_MAP = [
  { label: t('NetworkConfig.Modes.RouterMode'), type: 'router', systemMode: 0 },
  { label: t('NetworkConfig.Modes.APMode'), type: 'ap', systemMode: 1 },
  { label: t('NetworkConfig.Modes.RelayMode'), type: 'relay', systemMode: 4 },
]

onMounted(() => {
  getModeInfo()
})

const systemMode: any = ref('')

const getModeInfo = async () => {
  const res = await $post('', { requestType: 206 })
  if (res.msg === 'success') {
    console.log(res.info.systemMode, MODE_MAP)
    systemMode.value = res.info.systemMode
  }
}
</script>

<template>
  <div class="tabBox">
    <div
      v-for="item in tabList"
      class="tabText"
      @click="activeTab = item.value"
    >
      <VBtn
        v-if="activeTab == item.value"
        color="primary"
      >
        {{ item.text }}
      </VBtn>
      <VBtn
        v-else
        color="secondary"
        variant="text"
      >
        {{ item.text }}
      </VBtn>
    </div>
  </div>
  <div class="tabPage">
    <WanPage
      v-if="activeTab == 0"
      :system-mode="systemMode"
    />
    <NetworkPage v-if="activeTab == 1" />
    <IPPage v-if="activeTab == 2" />
    <PortPage v-if="activeTab == 3" />
    <LanPage v-if="activeTab == 4" />
    <DhcpPage v-if="activeTab == 5" />
  </div>
</template>

<style lang="scss" scoped>
.tabBox {
  display: flex;
  margin-block-end: 10px;
}
</style>
