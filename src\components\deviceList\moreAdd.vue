<script lang="ts" setup>
const emits = defineEmits(['close'])
const selectedRowsAdd = ref<any[]>([])

// Data table options
const sortBy = ref()
const orderBy = ref()

// Update data table options
const updateOptionsAdd = (options: any) => {
  sortBy.value = options.sortBy[0]?.key
  orderBy.value = options.sortBy[0]?.order
}

// 表头
const headersAdd = [
  { title: '名称', key: 'name' },
  { title: '型号', key: 'sn' },
  { title: 'IP', key: 'ip' },
  { title: 'MAC', key: 'mac' },
  { title: '发现时间', key: 'time' },
  { title: '在线状态', key: 'status' },
]

// 数据
const productsDataAdd = ref({
  total: 3,
  products: [
    {
      name: '',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: true,
    },
    {
      name: '',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '',
      mac: '',
      status: false,
    },
    {
      name: '',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: true,
    },
  ],
})

// 动态计算表格数据
const productsAdd = computed((): any[] => productsDataAdd.value.products)

const addMore = () => {
  emits('close')
}

const closeMask = () => {
  emits('close')
}
</script>

<template>
  <div class="mainBox">
    <div class="resultText">
      扫描结果
    </div>
    <!-- 👉 Datatable  -->
    <VDataTableServer
      v-model:model-value="selectedRowsAdd"
      :headers="headersAdd"
      :items="productsAdd"
      class="text-no-wrap"
      show-select
      :items-length="productsDataAdd.total"
      :no-data-text="t('NoData')"
      @update:options="updateOptionsAdd"
    >
      <!-- status -->
      <template #item.status="{ item }">
        <VChip
          v-if="item.status"
          color="success"
          label
          size="small"
        >
          在线
        </VChip>
        <VChip
          v-else
          color="error"
          label
          size="small"
        >
          离线
        </VChip>
      </template>
      <template #bottom />
    </VDataTableServer>
    <div class="btnBox">
      <VBtn
        color="primary"
        class="mr-2"
        @click="addMore"
      >
        批量添加
      </VBtn>
      <VBtn
        color="secondary"
        @click="closeMask"
      >
        取消
      </VBtn>
    </div>
  </div>
</template>

<style scoped>
.mainBox {
  padding: 32px;
  margin-block-end: 32px;
}

.resultText {
  font-family: "PingFang SC";
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  margin-block-end: 28px;
}

.btnBox {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-block-start: 32px;
}
</style>
