export const COOKIE_MAX_AGE_1_YEAR = 365 * 24 * 60 * 60

export const WIRELESS_NETWORK_TYPE = [
  {
    label: '2.4G',
    value: 0,
  },
  {
    label: '5G',
    value: 1,
  },
  {
    label: '2.4G/5G',
    value: 2,
  },
]

export const NETWORK_TYPE = [
  {
    label: 'LAN',
    value: 0,
  },
  {
    label: 'WAN',
    value: 1,
  },
]

export const SWITCH_TYPE = [
  {
    label: 'On',
    value: 0,
  },
  {
    label: 'Off',
    value: 1,
  },
]

export const ENCRYPTION_TYPE = [
  {
    label: 'NONE',
    value: 'none',
  },
  {
    label: 'WPA1/WPA2',
    value: 'mixed-psk',
  },
  {
    label: 'WPA3',
    value: 'psk2',
  },
]

export const SPEED_LIMIT_TYPE = [
  {
    label: 'speedLimit.off',
    value: 0,
  },
  {
    label: 'speedLimit.static',
    value: 1,
  },
]

export const ROAMING_PROTOCOL = [
  {
    label: '802.11r',
    value: 0,
  },
]

export const TEMPLATE_DISTRIBUTION_METHOD = [
  {
    label: 'template.immediate',
    value: 0,
  },
]

// {
//   label: "定时下发",
//     value: 1,
// },

export const DATE_FILTER_OPTIONS = [
  { title: 'dateFilter.day', value: 0 },
  { title: 'dateFilter.week', value: 1 },
  { title: 'dateFilter.month', value: 2 },
  { title: 'dateFilter.year', value: 3 },
]

export const NETWORK_EVENT_LEVEL = [
  {
    title: 'eventLevel.notification',
    value: 0,
  },
  {
    title: 'eventLevel.minor',
    value: 1,
  },
  {
    title: 'eventLevel.normal',
    value: 2,
  },
  {
    title: 'eventLevel.severe',
    value: 3,
  },
]
export const NETWORK_EVENT_LEVEL_FILTER = [
  {
    title: 'eventLevel.all',
    value: -1,
  },
  {
    title: 'eventLevel.notification',
    value: 0,
  },
  {
    title: 'eventLevel.minor',
    value: 1,
  },
  {
    title: 'eventLevel.normal',
    value: 2,
  },
  {
    title: 'eventLevel.severe',
    value: 3,
  },
]
export const NETWORK_EVENT_TYPE = [
  {
    title: 'eventType.fault',
    value: 0,
  },
  {
    title: 'eventType.load',
    value: 1,
  },
]

export const WIFI_NETWORK_TYPE = [
  {
    label: '2.4G',
    value: 0,
  },
  {
    label: '5G',
    value: 1,
  },
]

export const TERMINAL_STATUS_FILTER_OPTIONS = [
  {
    label: 'All',
    value: 0,
  },
  {
    label: 'terminal.online',
    value: 1,
  },
  {
    label: 'terminal.offline',
    value: 2,
  },
]

export const TERMINAL_NETWORK_TYPE_FILTER_OPTIONS = [
  {
    label: 'All',
    value: 0,
  },
  {
    label: 'terminal.wired',
    value: 1,
  },
  {
    label: 'terminal.wireless',
    value: 2,
  },
]

export const EVENT_STATUS = [
  { label: 'All', value: -1 },
  {
    label: 'event.unhandled',
    value: 0,
  },
  {
    label: 'event.handled',
    value: 2,
  },
  {
    label: 'event.ignored',
    value: 1,
  },
]

export const DATE_FREQUENCY = [
  { label: 'date.daily', value: 0 },
  { label: 'date.monday', value: 1 },
  { label: 'date.tuesday', value: 2 },
  { label: 'date.wednesday', value: 3 },
  { label: 'date.thursday', value: 4 },
  { label: 'date.friday', value: 5 },
  { label: 'date.saturday', value: 6 },
  { label: 'date.sunday', value: 7 },
]

// 802.11ax：11axg
// 802.11n：11ng
// 802.11g：11g
// 802.11b：11b
export const PROTOCOL_2G = [
  {
    label: '802.11ax',
    value: '11axg',
  }, {
    label: '802.11n',
    value: '11ng',
  }, {
    label: '802.11g',
    value: '11g',
  }, {
    label: '802.11b',
    value: '11b',
  },
]

// 802.11ax：11axa
// 802.11ac：11ac
// 802.11n/a：11na：
// 802.11a：11a
export const PROTOCOL_5G = [
  {
    label: '802.11ax',
    value: '11axa',
  }, {
    label: '802.11ac',
    value: '11ac',
  }, {
    label: '802.11n/a',
    value: '11na',
  }, {
    label: '802.11a',
    value: '11a',
  },
]

export enum CountryEnum {
  US = '0', // 美国
  CN = '1', // 中国
  AR = '2', // 阿根廷
  BD = '3', // 孟加拉国
  BR = '4', // 巴西
  CL = '5', // 智利
  CO = '6', // 哥伦比亚
  CR = '7', // 哥斯达黎加
  EC = '8', // 厄瓜多尔
  HK = '9', // 香港
  JM = '10', // 牙买加
  JO = '11', // 约旦
  JP = '12', // 日本
  LK = '13', // 斯里兰卡
  MX = '14', // 墨西哥
  MY = '15', // 马来西亚
  NZ = '16', // 新西兰
  PA = '17', // 巴拿马
  PE = '18', // 秘鲁
  PH = '19', // 菲律宾
  PK = '20', // 巴基斯坦
  PR = '21', // 波多黎各
  SG = '22', // 新加坡
  TW = '23', // 台湾
  UY = '24', // 乌拉圭
  VE = '25', // 委内瑞拉
  IN = '26', // 印度
  KR = '27', // 韩国
}

export const COUNTRY_OPTIONS = Object.entries(CountryEnum)
  .map(([key, _]) => ({
    label: key,
    value: key,
  }))

// 2.4G 信道
// 以下是国家代码对应的2.4G信道
export const CHANNEL_ARR_2G = [
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'], // US
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // CN
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // AR
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // BD
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // BR
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // CL
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // CO
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // CR
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // EC
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // HK
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // JM
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // JO
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // JP
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // LK
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // MK
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // MY
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // NZ
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // PA
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // PE
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // PH
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // PK
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'], // PR
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // SG
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'], // TW
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // UY
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // VE
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // IN
  ['auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], // KR
]

// 5G 信道
// 以下是国家代码对应的5G信道
export const CHANNEL_ARR_5G = [
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '144', '149', '153', '157', '161', '165'], // US
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '149', '153', '157', '161', '165'], // CN
  ['auto', '52', '56', '60', '64', '149', '153', '157', '161', '165'], // AR
  ['auto', '149', '153', '157', '161', '165'], // BD
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '144', '149', '153', '157', '161', '165'], // BR
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '144', '149', '153', '157', '161', '165'], // CL
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '144', '149', '153', '157', '161', '165'], // CO
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '144', '149', '153', '157', '161', '165'], // CR
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '144', '149', '153', '157', '161', '165'], // EC
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '149', '153', '157', '161', '165'], // HK
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '144', '149', '153', '157', '161', '165'], // JM
  ['auto', '36', '40', '44', '48', '149', '153', '157', '161', '165'], // JO
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140'], // JP
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '149', '153', '157', '161', '165'], // LK
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '144', '149', '153', '157', '161', '165'], // MK
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '149', '153', '157', '161', '165'], // MY
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '149', '153', '157', '161', '165'], // NZ
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '149', '153', '157', '161', '165'], // PA
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '149', '153', '157', '161', '165'], // PE
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '149', '153', '157', '161', '165'], // PH
  ['auto', '149', '153', '157', '161', '165'], // PK
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '149', '153', '157', '161', '165'], // PR
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '149', '153', '157', '161', '165'], // SG
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '144', '149', '153', '157', '161', '165'], // TW
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '149', '153', '157', '161', '165'], // UY
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '149', '153', '157', '161', '165'], // VE
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '144', '149', '153', '157', '161', '165', '169', '173'], // IN
  ['auto', '36', '40', '44', '48', '52', '56', '60', '64', '100', '104', '108', '112', '116', '120', '124', '128', '132', '136', '140', '144', '149', '153', '157', '161', '165'], // KR
]

// 20M：HT20
// 20M/40M自动：HT40
// 40M：HT40

export const BAND_WIDTH_2G = [{ label: '20M', value: '20M' }, { label: '20M/40M', value: '20/40M' }, { label: '40M', value: '40M' }]

// 20M：HT20
// 40M：HT40
// 80M：HT80
// 160M：HT160
export const BAND_WIDTH_5G = [
  {
    label: '20M',
    value: 'HT20',
  },
  {
    label: '40M',
    value: 'HT40',
  },
  {
    label: '80M',
    value: 'HT80',
  },
  {
    label: '160M',
    value: 'HT160',
  },
]

// 穿墙：空值
// 普通：20
// 节能：5
export const TX_POWER_2G = [
  {
    label: 'txPower.penetration',
    value: '',
  },
  {
    label: 'txPower.normal',
    value: '20',
  },
  {
    label: 'txPower.saving',
    value: '5',
  },
]

// 穿墙：空值
// 普通：23
// 节能：8
export const TX_POWER_5G = [
  {
    label: 'txPower.penetration',
    value: '',
  },
  {
    label: 'txPower.normal',
    value: '23',
  },
  {
    label: 'txPower.saving',
    value: '8',
  },
]

// 0：路由模式 1：AP模式 2：VLAN模式
export const NET_TYPE = [{
  label: 'NetworkConfig.Modes.RouterMode',
  value: '0',
}, {
  label: 'NetworkConfig.Modes.APMode',
  value: '1',
}, {
  label: 'NetworkConfig.Modes.VLANMode',
  value: '2',
}]

// DHCP配置范围限制
export const DHCP_RANGE = {
  MIN: 1,
  MAX: 6,
}
