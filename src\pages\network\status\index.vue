<script lang="ts" setup>
import { useTheme } from 'vuetify'
import { NETWORK_EVENT_LEVEL, NETWORK_EVENT_TYPE } from '@/utils/constants'
import { getLineChartSimpleConfig } from '@core/libs/apex-chart/apexCharConfig'

const vuetifyTheme = useTheme()

const balanceChartConfig = computed(() => {
  return {
    ...getLineChartSimpleConfig(vuetifyTheme.current.value),
    colors: ['#24B364', '#E64449'],
    stroke: {
      curve: 'straight',
      width: 1.5,
      lineCap: 'round',
    },
    legend: {
      show: false,
    },
    tooltip: {
      custom(data: any) {
        return `<div class='bar-chart pa-2'>
          <div>在线：${data.series[0][data.dataPointIndex]}</div>
          <div>离线：${data.series[1][data.dataPointIndex]}</div>
        </div>`
      },
    },
    grid: {
      padding: { top: -10 },
      strokeDashArray: 4,
      xaxis: {
        lines: { show: true },
      },
    },
    markers: {
      size: 2,
      strokeWidth: 1.5,
      strokeOpacity: 1,
      strokeColors: ['#24B364', '#E64449'],
      colors: ['#fff', '#fff'],
      hover: {
        size: 3,
      },
    },
    xaxis: {
      axisBorder: { show: false },
      crosshairs: {
        stroke: {
          show: true,
          width: 1,
          color: '#B6B6B6',
          dashArray: 0,
        },
      },
      categories: [
        '7/12',
        '8/12',
        '9/12',
        '10/12',
        '11/12',
        '12/12',
        '13/12',
        '14/12',
        '15/12',
        '16/12',
        '17/12',
        '18/12',
        '19/12',
        '20/12',
        '21/12',
      ],
    },
  }
})

const series = [
  {
    data: [280, 200, 220, 180, 270, 250, 70, 90, 200, 150, 160, 100, 150, 100, 50],
  },
  {
    data: [120, 100, 80, 90, 70, 60, 50, 40, 30, 20, 10, 0, 0, 0, 0],
  },
]

const productsData = ref({
  total: 3,
  products: [
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: 0,
    },
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: 1,
    },
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: 2,
    },
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: 3,
    },
  ],
})

const headers = [
  { title: '事件等级', key: 'status' },
  { title: '事件类型', key: 'sn' },
  { title: '事件名称', key: 'no' },
  { title: '事件次数', key: 'ip' },
  { title: '影响设备数', key: 'ip' },
  { title: '影响终端数', key: 'ip' },
  { title: '发生时间', key: 'ip' },
  { title: '待处理事件数', key: 'ip' },
  { title: '详情', key: 'detail' },
]

const network_level_colors = ['default',
  'info',
  'warning',
  'error']

const totalProduct = computed(() => productsData.value.products.length + 100)
const page = ref(1)
</script>

<template>
  <div class="network-status">
    <VRow class="mb-6">
      <VCol cols="6">
        <VCard>
          <template #title>
            <div class="d-flex align-start justify-space-between">
              <div class="mr-2 text-h5">
                <div class="title">
                  设备状态
                </div>
                <div class="sub-title">
                  最近4小时
                </div>
              </div>
              <div>
                <div class="font-weight-regular detail d-flex align-center">
                  查看详情
                  <VIcon icon="tabler-chevron-right" />
                </div>
              </div>
            </div>
          </template>
          <VCardText>
            <div class="status-statistic mb-4">
              <div class="status-statistic-item">
                <div class="label">
                  设备总数
                </div>
                <div class="num">
                  45
                </div>
              </div>
              <div class="status-statistic-item success">
                <div class="label">
                  在线
                </div>
                <div class="num">
                  45
                </div>
              </div>
              <div class="status-statistic-item error">
                <div class="label">
                  离线
                </div>
                <div class="num">
                  45
                </div>
              </div>
            </div>
            <div>
              <VueApexCharts
                type="line"
                height="160"
                :options="balanceChartConfig"
                :series="series"
              />
            </div>
          </VCardText>
        </VCard>
      </VCol>
      <VCol cols="6">
        <VCard>
          <template #title>
            <div class="d-flex align-start justify-space-between">
              <div class="mr-2 text-h5">
                <div class="title">
                  终端状态
                </div>
                <div class="sub-title">
                  最近4小时
                </div>
              </div>
              <div>
                <div class="font-weight-regular detail d-flex align-center">
                  查看详情
                  <VIcon icon="tabler-chevron-right" />
                </div>
              </div>
            </div>
          </template>
          <VCardText>
            <div class="status-statistic mb-4">
              <div class="status-statistic-item">
                <div class="label">
                  设备总数
                </div>
                <div class="num">
                  45
                </div>
              </div>
              <div class="status-statistic-item success">
                <div class="label">
                  在线
                </div>
                <div class="num">
                  45
                </div>
              </div>
              <div class="status-statistic-item error">
                <div class="label">
                  离线
                </div>
                <div class="num">
                  45
                </div>
              </div>
            </div>
            <div>
              <VueApexCharts
                type="line"
                height="160"
                :options="balanceChartConfig"
                :series="series"
              />
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <VCard>
      <template #title>
        <div class="d-flex align-start justify-space-between">
          <div class="mr-2 text-h5 d-flex align-center">
            <div class="title">
              事件列表
            </div>
            <VBtn
              class="ml-4"
              color="success"
              variant="tonal"
              disabled
              size="small"
            >
              总数：42
            </VBtn>
          </div>
          <div>
            <VMenu>
              <template #activator="{ props }">
                <VBtn
                  class="dropdown-selector mr-4"
                  v-bind="props"
                  variant="outlined"
                  color="secondary"
                >
                  全部
                  <template #append>
                    <VIcon icon="tabler-chevron-down" />
                  </template>
                </VBtn>
              </template>

              <VList
                :items="NETWORK_EVENT_LEVEL"
                item-title="title"
                item-value="value"
              />
            </VMenu>

            <VMenu>
              <template #activator="{ props }">
                <VBtn
                  class="dropdown-selector"
                  v-bind="props"
                  variant="outlined"
                  color="secondary"
                >
                  全部
                  <template #append>
                    <VIcon icon="tabler-chevron-down" />
                  </template>
                </VBtn>
              </template>

              <VList
                :items="NETWORK_EVENT_TYPE"
                item-title="title"
                item-value="value"
              />
            </VMenu>
          </div>
        </div>
      </template>

      <VDivider />

      <VDataTableServer
        :items="productsData.products"
        :headers="headers"
        :items-length="totalProduct"
        :no-data-text="t('NoData')"
      >
        <template #item.status="{ item }">
          <div class="network-event-tag">
            <VChip
              :color="network_level_colors[item.status]"
              variant="outlined"
            >
              {{ NETWORK_EVENT_LEVEL[item.status].title }}
            </VChip>
          </div>
        </template>
        <template #item.detail="{ item }">
          <VBtn variant="text">
            查看
          </VBtn>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="10"
            :total-items="totalProduct"
            :show-meta="true"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </div>
</template>

<style lang="scss" scoped>
.network-status {
  .title {
    font-size: 18px;
    font-weight: 500;
  }

  .sub-title {
    color: rgba(var(--v-theme-on-surface), 0.55);
    font-size: 15px;
    font-weight: normal;
  }

  .detail {
    color: rgba(var(--v-theme-on-surface), 0.7);
    cursor: pointer;
    font-size: 15px;
  }

  .status-statistic {
    display: grid;
    column-gap: 10px;
    grid-template-columns: repeat(3, 1fr);

    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      background: rgba($color: var(--v-theme-on-surface), $alpha: 8%);
      block-size: 76px;
      inline-size: 100%;

      .label {
        color: rgba(var(--v-theme-on-surface), 0.9);
        font-size: 15px;
      }

      .num {
        color: rgb(var(--v-theme-on-surface), 0.9);
        font-size: 28px;
        font-weight: 500;
      }

      &.success {
        background: rgba($color: var(--v-theme-success), $alpha: 8%);

        .num {
          color: rgb(var(--v-theme-success));
        }
      }

      &.error {
        background: rgba($color: var(--v-theme-error), $alpha: 8%);

        .num {
          color: rgb(var(--v-theme-error));
        }
      }
    }
  }
}
</style>

<style lang="scss">
.dropdown-selector {
  padding: unset;

  .v-btn__content {
    padding-block: 0;
    padding-inline: 20px;
  }

  .v-btn__append {
    margin: unset;
    block-size: 100%;
    border-inline-start: 1px solid rgb(var(--v-theme-secondary));
    padding-block: 0;
    padding-inline: 8px;
  }
}
</style>
