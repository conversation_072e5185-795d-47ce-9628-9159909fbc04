<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import { useTheme } from 'vuetify'
import { getAreaChartSplineConfig } from '@core/libs/apex-chart/apexCharConfig'

const { t } = useI18n()
const vuetifyTheme = useTheme()
const timeList = ref([] as Array<string>)

const uploadList = ref([] as Array<number>)
const downloadList = ref([] as Array<number>)
const fullTimeList = [] as Array<string>

const resetData = () => {
  $post('', {
    requestType: 222,
  }).then(res => {
    if (res.msg === 'success') {
      const list = res.info.hour
      const time: Array<string> = []
      const up: Array<number> = []
      const down: Array<number> = []

      list.forEach((item: any) => {
        fullTimeList.push(`${item.date.year}-${item.date.month}-${item.date.day} ${item.time.hour}:${item.time.minute < 10 ? `0${item.time.minute}` : `${item.time.minute}`}`)
        time.push(`${item.time.hour}: ${item.time.minute < 10 ? `0${item.time.minute}` : `${item.time.minute}`}`)
        up.push(+(item.tx / (1024 * 1024)).toFixed(2))
        down.push(+(item.rx / (1024 * 1024)).toFixed(2))
      })
      timeList.value = time
      uploadList.value = up
      downloadList.value = down
    }
    else {
      console.error(`${t('DataFetchFailed')}:`, res.err_msg)
    }
  }).catch(err => {
    console.error(`${t('RequestError')}:`, err)
  })
}

onMounted(() => {
  // resetData()
})

// const chartConfig = computed(() => getAreaChartSplineConfig(vuetifyTheme.current.value))
const chartConfig = computed(() => ({
  ...getAreaChartSplineConfig(vuetifyTheme.current.value),

  // 新增平滑曲线配置
  stroke: {
    curve: 'smooth', // 核心平滑配置
    width: 3, // 适当增加线宽
  },

  // 调整填充透明度
  fill: {
    type: 'solid', // 核心修改点
    opacity: 0.2, // 统一透明度
  },
  tooltip: {
    custom(data: any) {
      return `<div class= 'bar-chart pa-2'>
      <div class="text-h6 mb-4" > ${t('RouterTotalRate')} </div>
      <div class= "text-subtitle-2 text-secondary mb-1" > ${t('Time')} </div>
      <div div class= "text-subtitle-2 mb-4" > ${fullTimeList[data.dataPointIndex]} </div>
      <div class= "text-subtitle-2 text-secondary mb-1" > ${t('Upload')} </div>
      <div class= "text-subtitle-2 text-success mb-4" > ${data.series[0][data.dataPointIndex]}MB </div>
      <div class= "text-subtitle-2 text-secondary mb-1" > ${t('Download')} </div>
      <div class= "text-subtitle-2 text-primary" > ${data.series[1][data.dataPointIndex]}MB </div>
      </div>`
    },
  },
  legend: {
    show: true,
    position: 'bottom', // 位置在底部
    horizontalAlign: 'center', // 水平居中
    itemMargin: {
      horizontal: 8, // 图例项水平间距
      vertical: 4, // 图例项垂直间距
    },
    markers: {
      width: 10, // 颜色标记宽度
      height: 10, // 颜色标记高度
      radius: 5, // 圆角半径
      offsetY: 1, // 垂直偏移量
    },
  },
  colors: [ // 显式定义颜色序列
    '#28c76f', // 上传颜色
    '#4080ff', // 下载颜色
  ],
  xaxis: {
    categories: timeList.value,
  },
}))

// 修改 series 为计算属性（关键修改）
const series = computed(() => [
  {
    name: t('Upload'),
    data: uploadList.value,
  },
  {
    name: t('Download'),
    data: downloadList.value,
  },
])
</script>

<template>
  <VueApexCharts
    type="area"
    height="330"
    :options="chartConfig"
    :series="series"
  />
</template>
